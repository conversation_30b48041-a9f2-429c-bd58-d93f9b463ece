# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production (includes type-checking)
- `npm run type-check` - Run TypeScript type checking
- `npm run preview` - Preview production build locally

## Architecture Overview

This is a Vue 3 workflow editor application built with TypeScript, using Vue Flow for the workflow canvas. The app allows users to create and manage AI-powered workflows with visual node-based programming.

### Key Technologies
- **Vue 3** with Composition API
- **TypeScript** for type safety
- **Vue Flow** (@vue-flow/core) for the workflow canvas
- **Pinia** for state management
- **Vue Router** for routing
- **Vue i18n** for internationalization
- **Tailwind CSS** + **DaisyUI** for styling
- **Monaco Editor** for code editing
- **Vite** for build tooling

### Core Architecture

#### Node System (`src/components/nodes/`)
The workflow editor is built around a sophisticated node system where each node represents a unit of functionality:

- **Node Registry** (`nodes.ts`): Central registry defining all available nodes with their types, configurations, UI components, and data contracts
- **Node Components**: Each node has two Vue components:
  - `<node_name>.vue` - Visual representation on the canvas
  - `<node_name>_cfg.vue` - Configuration UI in the sidebar
- **Node Types**: Includes LLM, HTTP, browser automation, code runners, loops, conditions, and more

#### State Management (`src/stores/`)
- **User Store** (`user.ts`): Manages user authentication and profile data
- **Navigation Store** (`curNav.ts`): Handles current navigation state

#### Composables (`src/composables/`)
- **useWorkflow** (`useWorkflow.ts`): Core workflow management logic including save/load, project handling
- **useNodeOptions** (`useNodeOptions.ts`): Node configuration and options management

#### API Layer (`src/api/`)
Organized by feature with dedicated modules for projects, runtime, user management, credits, etc.

### Key Concepts

#### Workflow Structure
Workflows are composed of connected nodes where:
- Each node has inputs and outputs with typed data contracts
- Nodes can inject variables from other nodes' outputs using `${nodeName.outputVar}` syntax
- The workflow canvas uses Vue Flow for visual node manipulation

#### Node Development
When creating new nodes:
1. Define in `nodes.ts` with type, config, outputs, and components
2. Create UI component extending `BaseNode`
3. Create configuration component using `useNodesData`
4. Add i18n strings to locale files

#### Variable Injection System
- Nodes can reference outputs from other nodes using template syntax
- `getLinkedVar()` utility provides available variables for autocomplete
- Variables are automatically injected at runtime based on node connections

### Development Environment

#### Proxy Configuration
The dev server proxies `/v1` requests to `http://localhost:8999` for API calls.

#### Base Path
Production builds use `/dashboard/` as the base path.

#### Internationalization
Supports English and Chinese with automatic language detection based on browser locale.

## Important Files

- `src/components/nodes/nodes.ts` - Node registry and type definitions
- `src/composables/useWorkflow.ts` - Core workflow management
- `src/utils/workflow_utils.ts` - Workflow manipulation utilities
- `docs/node_implementation_guide.md` - Detailed guide for creating new nodes
- `docs/data_types.md` - Node data type specifications