# Node Data Types Guide

This document outlines the various data types used in the `config` and `output` definitions of workflow nodes, as specified in `src/components/nodes/nodes.ts`.

## Data Types

Below is a list of the data types used by nodes for their configuration parameters (`config`) and output variables (`output`).

### `string`

*   **Description:** Represents a sequence of characters. This is the most common data type, used for everything from simple text inputs to complex template strings.
*   **Usage:**
    *   **`config`:** Used for user inputs such as URLs, prompts, model names, and file paths.
    *   **`output`:** Used for the results of operations like LLM responses, web page content, or formatted text from a template.
*   **Examples:**
    *   `llm` node: `model`, `system`, `prompt`
    *   `browser` node: `url`
    *   `http` node: `url`, `method`, `body`

### `number`

*   **Description:** Represents a numerical value. This can be an integer or a floating-point number.
*   **Usage:**
    *   **`config`:** Used for settings that require a numeric value, such as `max_tokens` in the `llm` node or `loop_sleep` in the `loop` node.
*   **Examples:**
    *   `llm` node: `max_tokens`
    *   `loop` node: `loop_sleep`

### `array`

*   **Description:** Represents an ordered list of values. The values can be of any type, including objects or other arrays.
*   **Usage:**
    *   **`config`:** Used for collections of items, such as HTTP headers, condition options, or Python package requirements.
*   **Examples:**
    *   `http` node: `headers`
    *   `condition` node: `options`
    *   `code_runner` node: `requirements`

### `object`

*   **Description:** Represents a collection of key-value pairs. This is a flexible data type used for complex, structured data.
*   **Usage:**
    *   **`config`:** Used for complex configurations, such as the `workflow` definition within a `loop` node or the `params` for a `code_runner` node.
    *   **`output`:** Used when a node produces structured data, such as the output of a `code_runner` or `json_param_extract` node.
*   **Examples:**
    *   `loop` node: `workflow`
    *   `code_runner` node: `params`
    *   `plugin` node: `plugin`

---

This guide should serve as a reference for understanding and utilizing the data types available for node development. For specific implementation details, always refer to the node definitions in `src/components/nodes/nodes.ts`.
