# 工作流分享功能 - API 文档

本文档详细描述了工作流分享功能的完整实现，包括 API 接口、数据模型和访问控制。

## 最后更新
- **版本**: v2.2
- **更新时间**: 2025-07-15
- **状态**: 已实现并部署（完成流控系统和实际工作流执行）

## 1. 概念说明

-   **工作流 (Workflow)**: 用户在平台创建的自动化流程。
-   **分享 (Share)**: 用户为某个工作流创建的一个分享链接，拥有独立的权限和设置。
-   **分享者 (Sharer/Owner)**: 创建分享链接的用户。
-   **访问者 (Visitor)**: 任何拥有分享链接的人。
-   **运行者 (Runner)**: 在分享页面执行工作流的用户，特指计费方式为 `runner` 时，需要登录并付费的用户。

## 2. API 端点

API 分为两大部分：

1.  **分享管理 API**: 需要用户登录认证，用于创建、管理自己的工作流分享设置。URL 前缀: `/v1/workflows/:uuid/shares`。
2.  **公开访问 API**: 无需登录（或部分需要登录）即可访问，用于查看、执行、复制被分享的工作流。URL 前缀: `/v1/shared/:share_uuid`。

## 3. 数据模型

### 3.1 WorkflowShare 模型
```go
type WorkflowShare struct {
    Base                    // ID, CreatedAt, UpdatedAt, DeletedAt
    WorkflowID    uint      // 关联的工作流 ID
    Workflow      *Workflow // 工作流对象
    OwnerID       uint      // 分享者用户 ID  
    Owner         *User     // 分享者用户对象
    ShareUUID     string    // 32位分享唯一标识符
    Title         string    // 分享标题
    Description   string    // 分享描述
    BillTo        string    // 计费方: "sharer" 或 "runner"
    IsEnabled     bool      // 是否启用
    ExpiresAt     *time.Time // 过期时间（可选）
    
    // 新增：流控设置（JSON存储）
    RateLimit     *ShareRateLimit `json:"rate_limit"`
    
    // 新增：UI控制配置（JSON存储）
    UIConfig      *ShareUIConfig  `json:"ui_config"`
}
```

### 3.2 ShareRateLimit 模型（新增）
```go
type ShareRateLimit struct {
    Enabled           bool `json:"enabled"`              // 是否启用流控
    DailyRunLimit     int  `json:"daily_run_limit"`      // 每日运行次数限制
    HourlyRunLimit    int  `json:"hourly_run_limit"`     // 每小时运行次数限制
    PerIPDailyLimit   int  `json:"per_ip_daily_limit"`   // 单IP每日限制
    CooldownSeconds   int  `json:"cooldown_seconds"`     // 运行间隔冷却时间（秒）
}
```

**流控机制说明**：
- `Enabled`: 流控开关，false时忽略所有限制
- `DailyRunLimit`: 整个分享每日总执行次数限制
- `HourlyRunLimit`: 每小时执行次数限制
- `PerIPDailyLimit`: 单个IP地址每日执行次数限制
- `CooldownSeconds`: 连续两次执行之间的最小间隔时间

**重要提醒**：
- **流控只对运行操作（run）生效**，不影响查看（view）和复制（copy）操作
- 运行操作会产生token消耗和计费成本，因此需要流控保护
- 查看和复制操作不产生直接计费成本，不受流控限制

**多级限制机制**：
- 所有限制条件都必须满足才能执行运行操作
- 限制检查顺序：单IP每日限制 → 总每日限制 → 每小时限制 → 冷却时间
- 支持设置为0来禁用特定类型的限制

### 3.3 ShareUIConfig 模型（新增）
```go
type ShareUIConfig struct {
    AllowView         bool                   `json:"allow_view"`         // 是否允许查看
    AllowRun          bool                   `json:"allow_run"`          // 是否允许运行
    AllowCopy         bool                   `json:"allow_copy"`         // 是否允许复制
    ShowWorkflowGraph bool                   `json:"show_workflow_graph"` // 是否显示工作流图
    CustomTheme       map[string]interface{} `json:"custom_theme"`       // 自定义主题
    WelcomeMessage    string                 `json:"welcome_message"`    // 欢迎信息
}
```

### 3.4 ShareUsageTracker 模型（新增）
```go
type ShareUsageTracker struct {
    ID            uint       `json:"id"`
    ShareID       uint       `json:"share_id"`
    Date          time.Time  `json:"date"`
    Hour          int        `json:"hour"`
    VisitorIP     string     `json:"visitor_ip"`
    RunCount      int        `json:"run_count"`
    LastRunAt     *time.Time `json:"last_run_at"`
}
```

### 3.5 WorkflowShareLog 模型
```go
type WorkflowShareLog struct {
    ID               uint           
    ShareID          uint           // 分享 ID
    Share            *WorkflowShare // 分享对象
    VisitorIP        string         // 访问者 IP
    VisitorUserAgent string         // 浏览器信息
    Action           string         // 操作类型
    AccessedAt       utils.Time     // 访问时间
}
```

### 3.6 操作类型 (Action Types)
- `view`: 查看基本信息
- `run`: 执行工作流
- `copy`: 复制工作流

### 3.7 默认配置
- **默认UI配置**: 
  - `AllowView`: true（允许查看）
  - `AllowRun`: true（允许运行）
  - `AllowCopy`: false（不允许复制）
  - `ShowWorkflowGraph`: true（显示工作流图）
- **默认流控配置**: 
  - `Enabled`: false（流控关闭）
  - `DailyRunLimit`: 100（每日100次）
  - `HourlyRunLimit`: 10（每小时10次）
  - `PerIPDailyLimit`: 20（单IP每日20次）
  - `CooldownSeconds`: 5（5秒冷却）

---

## 第一部分: 分享管理 API

> **注意**: 以下所有接口都需要用户登录认证。

### 1.1 创建工作流分享

-   **功能**: 为指定的工作流创建一个新的分享链接。
-   **Endpoint**: `POST /v1/workflows/:uuid/shares`
-   **URL 参数**:
    -   `uuid`: 工作流的 UUID。
-   **Request Body** (JSON):
    ```json
    {
    	"title": "分享标题",
    	"description": "分享的详细描述",
    	"bill_to": "sharer", // 计费方: 'sharer' (分享者) 或 'runner' (运行者)
    	"expires_at": 1672531199, // 过期时间的 Unix 时间戳 (秒)，可选
    	"rate_limit": { // 可选流控配置
    		"enabled": true,
    		"daily_run_limit": 50,
    		"hourly_run_limit": 5,
    		"per_ip_daily_limit": 10,
    		"cooldown_seconds": 10
    	},
    	"ui_config": { // 可选UI配置
    		"allow_view": true,
    		"allow_run": true,
    		"allow_copy": false,
    		"show_workflow_graph": true,
    		"welcome_message": "欢迎使用这个工作流",
    		"custom_theme": {}
    	}
    }
    ```
-   **Success Response** (200 OK):
    -   返回新创建的分享对象。
-   **Error Responses**:
    -   `400 Bad Request`: 请求参数错误，例如 `title` 为空，或 UI配置中所有权限均为 false。
    -   `404 Not Found`: 工作流不存在。

### 1.2 获取工作流的分享列表

-   **功能**: 获取指定工作流下所有分享链接的列表。
-   **Endpoint**: `GET /v1/workflows/:uuid/shares`
-   **URL 参数**:
    -   `uuid`: 工作流的 UUID。
-   **Success Response** (200 OK):
    ```json
    {
      "shares": [
        { ...share_object },
        { ...share_object }
      ]
    }
    ```
-   **Error Responses**:
    -   `404 Not Found`: 工作流不存在。

### 1.3 获取特定分享详情

-   **功能**: 获取单个分享链接的详细信息及其统计数据。
-   **Endpoint**: `GET /v1/workflows/:uuid/shares/:share_uuid`
-   **URL 参数**:
    -   `uuid`: 工作流的 UUID。
    -   `share_uuid`: 分享的 UUID。
-   **Success Response** (200 OK):
    ```json
    {
      "share": { ...share_object },
      "stats": {
        "views": 100,
        "runs": 20,
        "copies": 5
      }
    }
    ```
-   **Error Responses**:
    -   `403 Forbidden`: 无权访问该分享。
    -   `404 Not Found`: 工作流或分享不存在。

### 1.4 更新工作流分享

-   **功能**: 更新一个已存在的分享链接的设置。
-   **Endpoint**: `PUT /v1/workflows/:uuid/shares/:share_uuid`
-   **URL 参数**:
    -   `uuid`: 工作流的 UUID。
    -   `share_uuid`: 分享的 UUID。
-   **Request Body** (JSON, 所有字段均为可选):
    ```json
    {
    	"title": "新的分享标题",
    	"description": "新的描述",
    	"bill_to": "runner",
    	"is_enabled": false, // 是否禁用此分享
    	"expires_at": 1672531199, // 新的过期时间，传 0 或 null 表示永久有效
    	"rate_limit": { // 更新流控配置
    		"enabled": false,
    		"daily_run_limit": 100,
    		"hourly_run_limit": 10,
    		"per_ip_daily_limit": 20,
    		"cooldown_seconds": 5
    	},
    	"ui_config": { // 更新UI配置
    		"allow_view": true,
    		"allow_run": false,
    		"allow_copy": true,
    		"show_workflow_graph": false,
    		"welcome_message": "更新后的欢迎信息",
    		"custom_theme": {"color": "blue"}
    	}
    }
    ```
-   **Success Response** (200 OK):
    -   返回更新后的分享对象。
-   **Error Responses**:
    -   `400 Bad Request`: 请求参数错误。
    -   `403 Forbidden`: 无权访问。
    -   `404 Not Found`: 工作流或分享不存在。

### 1.5 删除工作流分享

-   **功能**: 永久删除一个分享链接。
-   **Endpoint**: `DELETE /v1/workflows/:uuid/shares/:share_uuid`
-   **URL 参数**:
    -   `uuid`: 工作流的 UUID。
    -   `share_uuid`: 分享的 UUID。
-   **Success Response** (200 OK):
    ```json
    {
    	"message": "Share deleted successfully"
    }
    ```
-   **Error Responses**:
    -   `403 Forbidden`: 无权访问。
    -   `404 Not Found`: 工作流或分享不存在。

### 1.6 获取分享访问日志

-   **功能**: 获取指定分享链接的访问日志。
-   **Endpoint**: `GET /v1/workflows/:uuid/shares/:share_uuid/logs`
-   **URL 参数**:
    -   `uuid`: 工作流的 UUID。
    -   `share_uuid`: 分享的 UUID。
-   **Query 参数**:
    -   `page`: 页码，默认 1
    -   `limit`: 每页数量，默认 20，最大 100
-   **Success Response** (200 OK):
    ```json
    {
      "logs": [
        {
          "id": 1,
          "visitor_ip": "***********",
          "visitor_user_agent": "Mozilla/5.0...",
          "action": "editor_view",
          "accessed_at": "2025-07-15T10:30:00Z"
        }
      ],
      "pagination": {
        "page": 1,
        "limit": 20,
        "total": 45,
        "has_next": true
      }
    }
    ```
-   **Error Responses**:
    -   `403 Forbidden`: 无权访问。
    -   `404 Not Found`: 工作流或分享不存在。

---

## 第二部分: 公开访问 API

> **注意**: 以下接口主要为无需登录的公开访问设计，部分操作（如复制、付费运行）可能需要访问者登录。

### 2.1 获取分享的基本信息

-   **功能**: 访问分享链接时，获取其基本信息用于展示。
-   **Endpoint**: `GET /v1/shared/:share_uuid`
-   **URL 参数**:
    -   `share_uuid`: 分享的 UUID。
-   **Success Response** (200 OK):
    ```json
    {
    	"share_uuid": "...",
    	"title": "分享标题",
    	"description": "分享描述",
    	"bill_to": "sharer",
    	"owner_name": "所有者用户名",
    	"workflow_name": "工作流名称",
    	"workflow_description": "工作流描述",
    	"created_at": "...",
    	"expires_at": "...",
    	"rate_limit": { // 流控配置
    		"enabled": false,
    		"daily_run_limit": 100,
    		"hourly_run_limit": 10,
    		"per_ip_daily_limit": 20,
    		"cooldown_seconds": 5
    	},
    	"ui_config": { // UI配置
    		"allow_view": true,
    		"allow_run": true,
    		"allow_copy": false,
    		"show_workflow_graph": true,
    		"welcome_message": "",
    		"custom_theme": {}
    	}
    }
    ```
-   **Error Responses**:
    -   `403 Forbidden`: 分享被禁用或已过期。
    -   `404 Not Found`: 分享不存在。

### 2.2 获取分享的工作流详情（包含执行器数据）

-   **功能**: 获取工作流的完整结构数据，包含执行器所需的数据。如果分享设置了 `ui_config.allow_view: true`，返回工作流详情；如果还允许运行（`allow_run: true`），则额外包含流控状态信息。
-   **Endpoint**: `GET /v1/shared/:share_uuid/workflow`
-   **URL 参数**:
    -   `share_uuid`: 分享的 UUID。
-   **Success Response** (200 OK):
    ```json
    {
        "uuid": "...", // 工作流 UUID
        "name": "工作流名称",
        "description": "工作流描述",
        "data": { ... }, // 工作流的图结构数据，包含执行器所需的所有数据
        "type": "...",
        "owner": "所有者用户名",
        "share_info": {
            "title": "分享标题",
            "description": "分享描述",
            "bill_to": "sharer",
            "ui_config": { ... },
            "rate_limit": {
                "enabled": true,
                "daily_run_limit": 100,
                "hourly_run_limit": 10,
                "per_ip_daily_limit": 20,
                "cooldown_seconds": 5
            },
            // 只有当 allow_run 为 true 时才包含此字段
            "rate_limit_status": {
                "enabled": true,
                "limits": {
                    "daily_run_limit": 100,
                    "hourly_run_limit": 10,
                    "per_ip_daily_limit": 20,
                    "cooldown_seconds": 5
                },
                "usage": {
                    "ip_daily_count": 5,
                    "daily_count": 45,
                    "hourly_count": 2,
                    "last_run_at": "2025-07-15T12:30:00Z"
                },
                "remaining": {
                    "daily": 55,
                    "hourly": 8,
                    "per_ip_daily": 15,
                    "cooldown_remaining": 0
                }
            }
        }
    }
    ```
-   **Error Responses**:
    -   `403 Forbidden`: 分享未开启查看权限。
    -   `404 Not Found`: 分享不存在。

### 2.3 执行分享的工作流

-   **功能**: 在执行器页面提交输入，运行工作流。
-   **Endpoint**: `POST /v1/shared/:share_uuid/executor`
-   **URL 参数**:
    -   `share_uuid`: 分享的 UUID。
-   **Request Body** (JSON):
    ```json
    {
    	"input": {
    		"param1": "value1",
    		"param2": 123
    	}
    }
    ```
-   **成功响应**: 
    -   **内容类型**: `text/event-stream` (流式输出)
    -   **响应格式**: 工作流执行的实时结果流
    -   **响应头**: 可能包含 `X-Log-ID` 用于日志追踪
-   **重要说明**:
    -   **流控检查**: 执行前会检查流控限制，超过限制返回429错误
    -   **计费处理**: 根据 `bill_to` 设置进行计费
    -   **实时执行**: 调用实际的工作流执行器进行处理
-   **Error Responses**:
    -   `401 Unauthorized`: 当 `bill_to: 'runner'` 时，访问者未登录。
    -   `403 Forbidden`: 分享未开启运行权限，或 `bill_to: 'runner'` 时运行者余额不足。
    -   `404 Not Found`: 分享不存在。
    -   `429 Too Many Requests`: 超过流控限制。

### 2.4 复制分享的工作流

-   **功能**: 将一个分享的工作流复制到自己的账户下。
-   **Endpoint**: `POST /v1/shared/:share_uuid/copy`
-   **前置条件**: 访问者必须已登录。
-   **URL 参数**:
    -   `share_uuid`: 分享的 UUID。
-   **Request Body** (JSON, 可选):
    ```json
    {
    	"name": "我的副本名称", // 可选，默认为 "Copy of [原工作流名]"
    	"description": "我的副本描述" // 可选
    }
    ```
-   **Success Response** (200 OK):
    ```json
    {
    	"message": "Workflow copy created successfully",
    	"workflow": {
    		"uuid": "...", // 新副本的 UUID
    		"name": "...",
    		"description": "...",
    		"created_at": "..."
    	}
    }
    ```
-   **Error Responses**:
    -   `401 Unauthorized`: 访问者未登录。
    -   `403 Forbidden`: 分享未开启查看权限（无法复制没有查看权限的工作流）。
    -   `404 Not Found`: 分享不存在。

## 4. 实现特性

### 4.1 访问控制与安全
- **权限验证**: 所有管理操作验证用户是否为工作流所有者
- **分享状态检查**: 自动验证分享是否启用且未过期
- **IP 追踪**: 记录所有访问的 IP 地址和用户代理
- **输入验证**: 全面的参数验证和业务规则检查

### 4.2 流控系统

#### 4.2.1 核心原则
- **只对运行操作进行流控**: 流控机制只应用于工作流执行（run操作）
- **保护计费资源**: 运行操作会产生token消耗和计费成本，需要限制防止滥用
- **不影响基础功能**: 查看和复制操作不受流控限制

#### 4.2.2 多级限制机制
- **每日总限制**: 整个分享每日最大执行次数
- **每小时限制**: 每小时最大执行次数
- **单IP每日限制**: 防止单个IP过度使用
- **冷却时间**: 连续执行之间的最小间隔

#### 4.2.3 实时监控功能
- **使用情况跟踪**: 按IP、日期、小时精确记录运行次数
- **剩余次数计算**: 实时计算各项限制的剩余次数
- **冷却时间监控**: 显示冷却时间剩余秒数
- **历史数据查询**: 支持查询运行历史记录

#### 4.2.4 错误处理
- **详细错误信息**: 不同类型限制的具体错误提示
- **429状态码**: 标准HTTP状态码支持
- **优雅降级**: 流控失败不影响其他功能

#### 4.2.5 性能优化
- **异步记录**: 使用情况记录异步处理
- **数据库索引**: 优化查询性能
- **批量统计**: 高效的聚合查询
- **内存友好**: 避免大量数据加载

### 4.3 UI控制系统（新增）
- **精细权限**: 分别控制查看、运行、复制权限
- **可视化控制**: 控制是否显示工作流图
- **自定义主题**: 支持自定义UI主题
- **欢迎信息**: 支持自定义欢迎文本

### 4.4 计费系统
- **分享者付费**: `bill_to: "sharer"` - 工作流所有者承担执行费用
- **运行者付费**: `bill_to: "runner"` - 执行者需要登录并承担费用
- **余额检查**: 运行者付费模式下自动检查用户余额

### 4.5 统计与监控
- **访问日志**: 详细记录所有分享访问行为
- **使用追踪**: 精确记录运行次数和时间
- **统计数据**: 提供访问量、执行次数、复制次数等统计
- **实时监控**: 支持分页查看访问日志

### 4.6 数据存储优化
- **JSON字段**: 流控和UI配置使用PostgreSQL JSON字段存储
- **GORM钩子**: 自动处理JSON字段的序列化和反序列化
- **默认配置**: 提供合理的默认配置值
- **向后兼容**: 支持从旧版本分享模式迁移

## 5. 错误处理

### 5.1 通用错误码
- `400 Bad Request`: 参数错误、验证失败
- `401 Unauthorized`: 未登录（运行者付费模式）
- `403 Forbidden`: 权限不足、分享被禁用、余额不足
- `404 Not Found`: 资源不存在
- `429 Too Many Requests`: 超过流控限制
- `500 Internal Server Error`: 服务器内部错误

### 5.2 业务规则验证
- 分享标题不能为空
- UI配置中至少启用一种权限（查看、运行、复制）
- 过期时间不能早于当前时间
- 工作流必须存在且用户有权限
- 流控限制检查（每日、每小时、单IP、冷却时间）

### 5.3 流控错误类型（仅运行操作）
- `"daily run limit exceeded"`: 超过每日执行次数限制
- `"hourly run limit exceeded"`: 超过每小时执行次数限制  
- `"per-IP daily limit exceeded"`: 超过单IP每日限制
- `"cooldown period not elapsed"`: 冷却时间未满

**注意**: 这些错误只会在执行工作流时出现，查看和复制操作不会触发流控错误。

## 6. 部署状态

✅ **已实现功能**:
- 完整的分享管理 API (6个端点)
- 公开访问 API (4个端点) - 合并了工作流详情和执行器数据接口
- 访问控制与权限系统
- 访问日志与统计系统
- 计费系统集成
- 流控系统（专门针对运行操作）
- UI控制系统（精细权限、自定义主题）
- 使用追踪系统
- 实际工作流执行（调用workflowCommonHandler）
- 数据库迁移支持

✅ **生产就绪特性**:
- 全面的错误处理
- 输入验证与安全检查
- 访问日志与监控
- 流控限制与防滥用（只对运行操作）
- 向后兼容的数据迁移
- 性能优化的数据库查询
- JSON字段存储优化
- GORM钩子自动处理
- 流式输出支持
- 实时工作流执行

✅ **前端集成要点**:
- 流控只影响运行操作，查看和复制不受限制
- 执行接口返回流式输出（text/event-stream）
- 429错误码表示流控限制触发
- 需要根据bill_to设置处理登录要求
- 支持实时显示流控状态和剩余次数
- 工作流详情接口合并了执行器数据，前端只需调用一个接口获取完整信息
- 流控状态信息只在允许运行时才包含在响应中