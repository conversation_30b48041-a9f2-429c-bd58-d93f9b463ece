# Node Implementation Guide (Revised)

This document provides a revised, more accurate step-by-step guide for developers to create new nodes in the workflow editor.

## Introduction

A "node" in our workflow system is a self-contained unit of functionality. Each node is composed of several key parts that define its appearance, behavior, and data handling. This guide will walk you through creating these components and registering your new node with the system.

A node's implementation is primarily divided into three parts:

1.  **Node Definition (`nodes.ts`):** A central registry where all nodes are defined. This definition includes the node's type, name, icon, associated Vue components, and its data contract (inputs and outputs).
2.  **Node UI Component (`<node_name>.vue`):** A Vue component responsible for rendering the node's visual representation on the workflow canvas.
3.  **Node Configuration Component (`<node_name>_cfg.vue`):** A Vue component that provides the user interface for configuring the node's parameters, displayed in the editor's sidebar.

## Step 1: Define the Node in `nodes.ts`

The first step is to create a definition for your new node in the `src/components/nodes/nodes.ts` file. This object is the "source of truth" for your node's properties.

1.  **Open `src/components/nodes/nodes.ts`**.
2.  **Add a new entry** to the `Nodes` object. The key for this entry should be a unique identifier for your node (e.g., `my_node`).

Here is a breakdown of the properties for a node definition:

-   `show` (boolean): Determines if the node is visible in the node selection menu.
-   `type` (string): A unique type identifier for the node. This should match the key in the `Nodes` object.
-   `label` (string): The label for the node, often used for internationalization (i18n).
-   `nodeShowName` (string): The name displayed in the node selection menu.
-   `icon` (Component): The icon component for the node (e.g., from `@heroicons/vue/24/solid`).
-   `node` (Component): The Vue component for the node's UI, loaded asynchronously with `defineAsyncComponent`.
-   `cfgRender` (Component): The Vue component for the node's configuration UI, also loaded asynchronously.
-   `config` (array): An array of objects defining the node's input parameters. Each object has:
    -   `name` (string): The parameter name.
    -   `type` (string): The data type (e.g., `string`, `number`, `boolean`, `object`).
    -   `default`: The default value.
    -   `injectVar` (boolean, optional): If true, the variable can be automatically injected from other nodes' outputs.
-   `output` (array): An array of objects defining the node's output variables. Each object has:
    -   `name` (string): The variable name.
    -   `type` (string): The data type.
    -   `desc` (string): A description of the output.
-   `group` (string): The group the node belongs to in the selection menu.
-   `helpDoc` (string, optional): A link to or content for help documentation.

**Example: `my_node` definition in `nodes.ts`**

```typescript
// src/components/nodes/nodes.ts

// ... other imports
import { BeakerIcon } from "@heroicons/vue/24/solid";

export const Nodes = {
	// ... other nodes
	my_node: {
		show: true,
		type: "my_node",
		label: "$nodes.my_node.label",
		nodeShowName: "$nodes.my_node.nodeShowName",
		icon: BeakerIcon,
		node: defineAsyncComponent(() => import("@/components/nodes/my_node.vue")),
		cfgRender: defineAsyncComponent(() => import("@/components/nodes/my_node_cfg.vue")),
		config: [
			{ name: "param1", type: "string", default: "default value", injectVar: true },
		],
		output: [{ name: "result", type: "string", desc: "$nodes.my_node.output_desc" }],
		group: "$nodes.group.custom",
		helpDoc: "$nodes.my_node.helpDoc",
	},
	// ... other nodes
} as NodesType;
```

## Step 2: Create the Node UI Component

This component renders the node on the canvas. It's best practice to use the `base_node.vue` component to maintain a consistent look and feel.

1.  **Create the file:** `src/components/nodes/my_node.vue`.
2.  **Implement the component:**

The `llm.vue` component provides a great, simple example of how to structure a node's UI. It uses a `BaseNode` component and passes the necessary data to it.

**Example: `my_node.vue`**

```vue
<template>
	<BaseNode>
		<template #body="{ data }">
			<p class="w-full flex items-center">
				<component
					v-if="iconComponent"
					:is="iconComponent"
					class="w-5 h-5 align-middle text-white bg-accent rounded-md p-1 box-border shrink-0"
				/>
				<span class="ml-2 text-ellipsis overflow-hidden whitespace-nowrap">{{
					data.name
				}}</span>
			</p>
			<p class="m-0 mt-1 text-xs font-thin">Param1: {{ data.input.param1 }}</p>
		</template>
	</BaseNode>
</template>

<script setup lang="ts">
	import BaseNode from "@/components/utils/base_node.vue";
	import { Nodes } from "./nodes.ts";
	import { useI18n } from "vue-i18n";

	const { t } = useI18n();
	const iconComponent = Nodes["my_node"].icon || null;
</script>
```

## Step 3: Create the Node Configuration Component

This component is for the sidebar and allows users to edit the node's parameters.

1.  **Create the file:** `src/components/nodes/my_node_cfg.vue`.
2.  **Implement the component:**

This component receives the node's `id` as a prop and uses `useNodesData` from `@vue-flow/core` to get a reactive reference to the node's data.

**Example: `my_node_cfg.vue`**

```vue
<template>
	<div v-if="nodeData">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<p class="mt-4">Parameter 1:</p>
		<EditorWithFullscreen
			v-model="nodeData.data.input.param1"
			:vars="vars"
			editor-type="code"
		/>

		<div class="divider"></div>
		<p>{{ t("output") }}:</p>
		<div
			v-for="i in nodeData.data.output"
			:key="i"
			class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
		>
			<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
			<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { useNodesData } from "@vue-flow/core";
	import { ref, onMounted } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import EditorWithFullscreen from "@/components/utils/editor_with_fullscreen.vue";
	import { useI18n } from "vue-i18n";

	const { t } = useI18n();
	const prop = defineProps(["id"]);
	const nodeData = useNodesData(prop.id);
	const vars = ref(getLinkedVar(prop.id));

	onMounted(() => {
		if (nodeData.value && !nodeData.value.data.input.param1) {
			nodeData.value.data.input.param1 = "default value";
		}
	});
</script>
```

## Step 4: Add Internationalization (i18n) Strings

To support multiple languages, add the necessary keys to the localization files in `src/locales/`.

**Example: `src/locales/en.json`**

```json
{
	"nodes": {
		"my_node": {
			"label": "My Node",
			"nodeShowName": "My Custom Node",
			"output_desc": "The result of my node's operation.",
			"helpDoc": "This is a custom node for demonstration."
		},
		"group": {
			"custom": "Custom Nodes"
		}
	}
}
```

## Conclusion

By following these four steps, you can create a new, fully functional node that integrates seamlessly into the existing workflow system. Remember to study the existing nodes for more complex examples and patterns.
