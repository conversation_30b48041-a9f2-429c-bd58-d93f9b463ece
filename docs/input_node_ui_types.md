# Input Node UI Types Guide

This document explains the specific UI types available in the **Input Node**. These types are configured in `input_cfg.vue` and determine the kind of form field presented to the end-user when a workflow is run.

It is crucial to understand that these types define the *user interface element* for input, which is distinct from the fundamental data types (`string`, `number`, `array`, `object`) that govern data flow between nodes.

## UI Types in the Input Node

When configuring an Input Node, you can add multiple input variables. Each variable has a `type` that corresponds to a specific UI component.

### `text`

*   **UI Element:** Renders a single-line text input field.
*   **Use Case:** For short text inputs like names, titles, or keywords.
*   **Output Data:** The value is a `string`.

### `longtext`

*   **UI Element:** Renders a multi-line text area.
*   **Use Case:** For longer blocks of text, such as descriptions, paragraphs, or articles.
*   **Output Data:** The value is a `string`.

### `select`

*   **UI Element:** Renders a dropdown menu (a `<select>` element).
*   **Use Case:** When the user must choose one option from a predefined list.
*   **Configuration:** Requires defining a list of options, where each option has a `value`.
*   **Output Data:** The `value` of the selected option, which is a `string`.

### `radio`

*   **UI Element:** Renders a set of radio buttons.
*   **Use Case:** Similar to `select`, but displays all options visibly at once. The user can only select one.
*   **Configuration:** Requires a list of options.
*   **Output Data:** The `value` of the selected radio button, which is a `string`.

### `checkbox`

*   **UI Element:** Renders a set of checkboxes.
*   **Use Case:** Allows the user to select zero, one, or multiple options from a list.
*   **Configuration:** Requires a list of options.
*   **Output Data:** An `array` of `string`s, containing the values of all selected checkboxes.

### `image`

*   **UI Element:** Renders a file upload component specifically for images.
*   **Use Case:** When the user needs to provide one or more images as input to the workflow.
*   **Configuration:** Allows setting a limit on the number of images that can be uploaded.
*   **Output Data:** The data structure for this is likely an array of objects, where each object contains information about the uploaded image (e.g., URL, base64 representation). This output can then be used by nodes that support vision capabilities.
