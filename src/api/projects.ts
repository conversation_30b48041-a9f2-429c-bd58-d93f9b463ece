import axios from "@/api/axios";

export type ProjectType = "workflow" | "project";

export type Project = {
	uuid: string;
	name: string;
	description: string;
	data: string;
	created_at: string;
	is_published?: boolean;
	parent_workflow_id: string;
	type: ProjectType;
};

export async function getProjects(
	page = 1,
	pageSize = 10,
	search = "",
	workflow_uuid: string = "0"
) {
	let res = await axios.get(
		`/projects?page=${page}&pageSize=${pageSize}&search=${search}&workflow_uuid=${workflow_uuid}`
	);
	return res.data;
}

export async function getProject(uuid: string, isBrief: boolean = false) {
	let res = await axios.get<Project>(`/projects/${uuid}?isBrief=${isBrief}`);
	return res.data;
}

export async function createProject(project: Project): Promise<string> {
	let res = await axios.post("/projects", project);
	return res.data;
}

export async function updateProject(uuid: string, project: Project) {
	return await axios.patch(`/projects/${uuid}`, project);
}

export async function deleteProject(uuid: string) {
	return await axios.delete(`/projects/${uuid}`);
}

export async function cloneProject(uuid: string) {
	return await axios.post(`/projects_clone/${uuid}`);
}

export type PublishedVersion = {
	id: string;
	name: string;
	version: string;
	is_active: boolean;
	updated_at: string;
};

export async function publishedVersions(uuid: string) {
	return await axios.get(`/projects/${uuid}/published_versions`);
}

// 获取项目组树 - 只返回project类型的项目（项目组）
export async function getProjectGroups(parentId: string = "0") {
	const res = await axios.get(`/projects/project_groups?parent_id=${parentId}`);
	return res.data;
}

// 移动项目到指定项目组
export async function moveProject(projectId: string, targetParentId: string) {
	const res = await axios.patch(`/projects/${projectId}/move`, {
		parent_workflow_id: targetParentId
	});
	return res.data;
}
