import axios from "@/api/axios";

export type CreditHistory = {
	id: number;
	created_at: string;
	updated_at: string;
	workflow_id: number;
	uid: number;
	llm_type: string;
	llm_input: number;
	llm_output: number;
	credits: number;
};

export type CreditHistoryResponse = {
	list: CreditHistory[];
	total: number;
};

export async function getCreditHistory(page = 1, pageSize = 20) {
	let res = await axios.get<CreditHistoryResponse>(
		`/credits/history?page=${page}&pageSize=${pageSize}`
	);
	return res.data;
}
