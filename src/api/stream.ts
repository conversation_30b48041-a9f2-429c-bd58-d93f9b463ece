import { BaseURL } from "./base";

export default async function eventStreamRequest(
	url: string,
	headers: object,
	body: object,
	onEvent: (event: string, data: any) => void
): Promise<string | null> {
	// const url = "/v1/workflow/runtime/debug";
	const response = await fetch(BaseURL + url, {
		method: "POST",
		headers: {
			"Content-Type": "text/event-stream",
			...headers,
		},
		body: JSON.stringify(body),
	});

	// 获取响应头中的 x-log-id
	const logId = response.headers.get("x-log-id");

	if (!response.body) {
		return logId;
	}
	const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();
	let buffer = "";

	while (true) {
		const { value, done } = await reader.read();
		if (done) break;

		// 将新读取的值添加到缓冲区
		buffer += value;

		// 分割完整的消息
		let index;
		while ((index = buffer.indexOf("\n\n")) !== -1) {
			const message = buffer.substring(0, index).trim();
			buffer = buffer.substring(index + 2);

			// 分割事件和数据
			const lines = message.split("\n");
			let eventType = null;
			let eventData = null;

			for (const line of lines) {
				if (line.startsWith("event:")) {
					eventType = line.substring("event:".length).trim();
				} else if (line.startsWith("data:")) {
					eventData = JSON.parse(line.substring("data:".length).trim());
				}
			}

			// 将解析后的事件类型和数据传递给 onEvent
			if (eventType && eventData) {
				onEvent(eventType, eventData);
			}
		}
	}

	// 返回 log ID
	return logId;
}
