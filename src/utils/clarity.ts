import type { User } from "@/api/user";

/**
 * 使用 Microsoft Clarity 标识用户
 * 根据微软文档：https://learn.microsoft.com/en-us/clarity/setup-and-installation/identify-api
 *
 * @param user 用户信息对象
 */
export function identifyUserWithClarity(user: User) {
	try {
		// 检查 Clarity 是否可用
		if (typeof window !== "undefined" && window.clarity) {
			// 格式：window.clarity("identify", userId, sessionId, pageId, friendlyName)
			const userId = user.id;
			const sessionId = `session-${user.id}-${Date.now()}`;
			const pageId = `page-${user.id}-${Date.now()}`;
			const friendlyName = user.username;

			// 调用 Clarity identify API
			window.clarity("identify", userId, sessionId, pageId, friendlyName);
		}
	} catch (error) {
		console.error(error);
	}
}

/**
 * 清除 Clarity 用户标识（用户登出时）
 */
export function clearClarityIdentity() {
	// clarity 没有这个功能，所以就不需要做
}

// 扩展 Window 接口以包含 clarity 方法
declare global {
	interface Window {
		clarity: (action: string, ...args: any[]) => void;
	}
}
