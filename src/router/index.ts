import { createRouter, createWebHistory } from "vue-router";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
NProgress.configure({
	showSpinner: false,
});
const router = createRouter({
	history: createWebHistory(import.meta.env.BASE_URL),
	routes: [
		// {
		// 	path: "/",
		// 	name: "home",
		// 	component: () => import("../views/IndexView.vue"),
		// 	meta: {
		// 		title: "首页",
		// 		requiresAuth: false,
		// 		inDashboard: false,
		// 	},
		// },
		{
			path: "/login",
			name: "login",
			component: () => import("../views/LoginView.vue"),
			meta: {
				title: "login",
				requiresAuth: false,
				inDashboard: false,
			},
		},
		{
			path: "/register",
			name: "register",
			component: () => import("../views/RegisterView.vue"),
			meta: {
				title: "signup",
				requiresAuth: false,
				inDashboard: false,
			},
		},
		{
			path: "/verify-email/:email?",
			name: "verify-email",
			component: () => import("../views/VerifyEmailView.vue"),
			meta: {
				title: "email_verification",
				requiresAuth: false,
				inDashboard: false,
			},
		},
		{
			path: "",
			name: "dashboard",
			redirect: "/projects",
		},
		{
			path: "/projects/:folderId?",
			name: "projects",
			component: () => import("../views/ProjectsView.vue"),
			props: true, // Pass route.params as component props
			meta: {
				title: "workflow",
				requiresAuth: true,
				inDashboard: true,
			},
		},
		{
			path: "/workflow/:id",
			name: "workflow",
			component: () => import("../views/WorkflowView.vue"),
			meta: {
				title: "workflow-edit",
				requiresAuth: true,
				inDashboard: true,
			},
		},
		{
			path: "/runtime/:id",
			name: "runtime",
			component: () => import("../views/RuntimeView.vue"),
			meta: {
				title: "runtime-workflow",
				requiresAuth: true,
				inDashboard: true,
			},
		},
		{
			path: "/setting",
			name: "setting",
			component: () => import("../views/SettingView.vue"),
			meta: {
				title: "settings",
				requiresAuth: true,
				inDashboard: true,
			},
		},
		{
			path: "/run_logs",
			name: "run_logs",
			component: () => import("../views/RunLogsView.vue"),
			meta: {
				title: "run_logs",
				requiresAuth: true,
				inDashboard: true,
			},
		},
		{
			path: "/api_management",
			name: "api_management",
			component: () => import("../views/ApiManagementView.vue"),
			meta: {
				title: "api_management",
				requiresAuth: true,
				inDashboard: true,
			},
		},
		{
			path: "/credits_history",
			name: "credits_history",
			component: () => import("../views/CreditsHistoryView.vue"),
			meta: {
				title: "credits_history",
				requiresAuth: true,
				inDashboard: true,
			},
		},
		{
			path: "/shared/:shareUuid",
			name: "shared-workflow",
			component: () => import("../views/SharedWorkflowView.vue"),
			meta: {
				title: "shared_workflow",
				requiresAuth: false,
				inDashboard: false,
			},
		},
	],
});
// 前置首位 访问权限控制
router.beforeEach((to) => {
	NProgress.start();
});
// 后置守卫
router.afterEach((to) => {
	NProgress.done();
});
export default router;
