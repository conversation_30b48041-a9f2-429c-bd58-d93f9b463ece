<script setup lang="ts">
	import { useCurNav } from "@/stores/curNav";
	import { type CreditHistory, getCreditHistory } from "@/api/credits";
	import { ref, watch, onMounted, defineAsyncComponent } from "vue";
	import { CurrencyDollarIcon } from "@heroicons/vue/24/outline";
	import component_wait from "@/components/utils/component_wait.vue";
	import CreditsBalance from "@/components/common/CreditsBalance.vue";

	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	// 延迟加载组件
	const Page = defineAsyncComponent(() => import("@/components/utils/page.vue"));

	const curNavStore = useCurNav();
	curNavStore.setCurNav("credits_history");

	const credits = ref<CreditHistory[]>([]);
	const creditCount = ref(0);
	const page = ref(1);
	const pageSize = ref(20);
	const loading = ref(true);

	const load = () => {
		loading.value = true;
		getCreditHistory(page.value, pageSize.value)
			.then((res) => {
				credits.value = res.list;
				creditCount.value = res.total;
			})
			.finally(() => {
				loading.value = false;
			});
	};

	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		return date.toLocaleString();
	};

	const formatCredits = (credits: number) => {
		return credits.toFixed(2);
	};

	const getModelBadgeClass = (modelType: string) => {
		if (modelType.toLowerCase().includes("gpt")) {
			return "badge-success";
		} else if (modelType.toLowerCase().includes("claude")) {
			return "badge-warning";
		} else {
			return "badge-info";
		}
	};

	watch(page, () => {
		load();
	});

	onMounted(() => {
		load();
	});
</script>

<template>
	<div class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-full p-6">
		<!-- 页面标题 -->
		<div class="mb-6">
			<div class="flex items-center space-x-4 mb-6">
				<div>
					<h1 class="text-3xl font-bold text-slate-800">
						{{ t("credits_history") }}
					</h1>
					<p class="text-slate-600 mt-1">{{ t("credits_history_desc") }}</p>
				</div>
			</div>

			<!-- 积分余额组件 -->
			<CreditsBalance />
		</div>

		<!-- 积分历史表格 -->
		<div
			class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-slate-200 overflow-hidden"
		>
			<div class="overflow-x-auto">
				<table class="table w-full">
					<thead>
						<tr class="bg-slate-50">
							<th class="text-left font-semibold text-slate-700">
								{{ t("llm_model") }}
							</th>
							<th class="text-left font-semibold text-slate-700">
								{{ t("input_tokens") }}
							</th>
							<th class="text-left font-semibold text-slate-700">
								{{ t("output_tokens") }}
							</th>
							<th class="text-left font-semibold text-slate-700">
								{{ t("total_tokens") }}
							</th>
							<th class="text-left font-semibold text-slate-700">
								{{ t("credits") }}
							</th>
							<th class="text-left font-semibold text-slate-700">
								{{ t("created_at") }}
							</th>
						</tr>
					</thead>
					<tbody>
						<!-- 加载状态 -->
						<template v-if="loading">
							<tr v-for="i in 10" :key="i" class="animate-pulse">
								<td><div class="h-4 bg-slate-200 rounded w-20"></div></td>
								<td><div class="h-4 bg-slate-200 rounded w-16"></div></td>
								<td><div class="h-4 bg-slate-200 rounded w-16"></div></td>
								<td><div class="h-4 bg-slate-200 rounded w-16"></div></td>
								<td><div class="h-4 bg-slate-200 rounded w-12"></div></td>
								<td><div class="h-4 bg-slate-200 rounded w-24"></div></td>
							</tr>
						</template>

						<!-- 数据行 -->
						<template v-if="!loading && credits.length > 0">
							<tr
								v-for="credit in credits"
								:key="credit.id"
								class="hover:bg-slate-50 transition-colors"
							>
								<td>
									<div class="flex items-center space-x-3">
										<span class="badge badge-success text-white">
											{{ credit.llm_type }}
										</span>
									</div>
								</td>
								<td class="font-mono text-sm">
									{{ credit.llm_input.toLocaleString() }}
								</td>
								<td class="font-mono text-sm">
									{{ credit.llm_output.toLocaleString() }}
								</td>
								<td class="font-mono text-sm font-semibold">
									{{
										(credit.llm_input + credit.llm_output).toLocaleString()
									}}
								</td>
								<td class="font-mono text-sm font-bold text-red-600">
									-{{ formatCredits(credit.credits) }}
								</td>
								<td class="text-sm text-slate-600">
									{{ formatDate(credit.created_at) }}
								</td>
							</tr>
						</template>

						<!-- 无数据状态 -->
						<tr v-if="!loading && credits.length === 0">
							<td colspan="6" class="text-center py-12">
								<div class="flex flex-col items-center space-y-4">
									<CurrencyDollarIcon class="w-12 h-12 text-slate-300" />
									<div>
										<p class="text-lg font-medium text-slate-600">
											{{ t("no_credits_history") }}
										</p>
										<p class="text-sm text-slate-500">
											{{ t("no_credits_history_desc") }}
										</p>
									</div>
								</div>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>

		<!-- 分页 -->
		<div class="mt-6" v-if="creditCount > 0">
			<component_wait>
				<Page
					:page="page"
					:pageSize="pageSize"
					:total="creditCount"
					@change="(e) => (page = e)"
				></Page>
			</component_wait>
		</div>
	</div>
</template>

<style scoped>
	.slide-fade-enter-active,
	.slide-fade-leave-active {
		transition: all 0.5s ease;
	}
	.slide-fade-enter-from,
	.slide-fade-leave-to {
		transform: translateX(100%);
		opacity: 0;
	}
</style>
