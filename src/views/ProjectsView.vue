<script setup lang="ts">
	import {
		type Project,
		getProjects,
		deleteProject,
		cloneProject,
		getProject,
	} from "@/api/projects";
	import { useCurNav } from "@/stores/curNav";
	import { ref, watch, defineAsyncComponent, onMounted } from "vue";
	import { useRouter, useRoute } from "vue-router";
	import {
		TrashIcon,
		DocumentDuplicateIcon,
		ArrowRightIcon,
	} from "@heroicons/vue/24/outline";
	import { useI18n } from "vue-i18n";
	import { Error } from "@/utils/notify";
	import DeleteConfirmDialog from "@/components/common/DeleteConfirmDialog.vue";
	import ProjectsLoading from "@/components/projects/ProjectsLoading.vue";
	import ProjectsBreadcrumb from "@/components/projects/ProjectsBreadcrumb.vue";
	import ProjectsEmptyState from "@/components/projects/ProjectsEmptyState.vue";
	import ProjectItem from "@/components/projects/ProjectItem.vue";
	import CreateProjectGroupDialog from "@/components/projects/CreateProjectGroupDialog.vue";
	import MoveProjectDialog from "@/components/projects/MoveProjectDialog.vue";
	import EditProjectDialog from "@/components/projects/EditProjectDialog.vue";
	import SharesManager from "@/components/shares/SharesManager.vue";
	import { PlusIcon } from "@heroicons/vue/24/solid";

	const { t } = useI18n();
	const Page = defineAsyncComponent(() => import("@/components/utils/page.vue"));
	const curNavStore = useCurNav();
	curNavStore.setCurNav("projects");

	const router = useRouter();
	const route = useRoute();

	// Define props to accept the folderId from the router
	defineProps<{
		folderId?: string;
	}>();

	const currentWorkflowUuid = ref("0");
	const breadcrumbs = ref<{ uuid: string; name: string }[]>([]);

	const projects = ref<Project[]>([]);
	const total = ref(0);
	const page = ref(1);
	const pageSize = ref(16);
	const loading = ref(false);

	// 删除确认对话框状态
	const showDeleteDialog = ref(false);
	const deleteTarget = ref<Project | null>(null);
	const isDeleting = ref(false);

	// 新建项目组对话框状态
	const showCreateGroupDialog = ref(false);

	// 移动项目对话框状态
	const showMoveDialog = ref(false);
	const moveTarget = ref<Project | null>(null);

	// 编辑项目对话框状态
	const showEditDialog = ref(false);
	const editTarget = ref<Project | null>(null);

	// 分享管理对话框状态
	const showSharesManager = ref(false);
	const shareTarget = ref<Project | null>(null);

	// Load projects based on the current folder UUID
	const loadProjects = () => {
		loading.value = true;
		getProjects(page.value, pageSize.value, "", currentWorkflowUuid.value)
			.then((res) => {
				projects.value = res.list;
				total.value = res.total;
			})
			.finally(() => {
				loading.value = false;
			});
	};

	// Function to load a specific folder by UUID
	const loadFolder = async (folderId: string | null | undefined) => {
		if (!folderId || folderId === "0") {
			// Root folder
			currentWorkflowUuid.value = "0";
			breadcrumbs.value = [];
		} else {
			// If we have a folderId, set it as the current folder
			currentWorkflowUuid.value = folderId;

			// If breadcrumbs are empty but we have a folderId, we need to fetch folder info
			// to build the breadcrumb trail.
			const existingBreadcrumb = breadcrumbs.value.find((b) => b.uuid === folderId);
			if (!existingBreadcrumb) {
				// Set a loading state for the breadcrumb
				breadcrumbs.value = [{ uuid: folderId, name: "Loading..." }];

				// Fetch the folder name using getProject API
				getProject(folderId, true)
					.then((project) => {
						breadcrumbs.value = [{ uuid: folderId, name: project.name }];
					})
					.catch((error) => {
						console.error("Error loading folder info:", error);
						// Fallback or error handling
						breadcrumbs.value = [{ uuid: folderId, name: "Unknown Folder" }];
					});
			}
		}

		// Load the folder contents
		loadProjects();
	};

	// Watch for page and pageSize changes
	watch([page, pageSize], () => {
		loadProjects();
	});

	// Watch for changes to currentWorkflowUuid
	watch(currentWorkflowUuid, () => {
		page.value = 1; // Reset to first page when changing directories
		loadProjects();
	});

	// Watch for changes to the route's folderId parameter
	watch(
		() => route.params.folderId,
		(newFolderId) => {
			if (newFolderId && newFolderId !== currentWorkflowUuid.value) {
				loadFolder(newFolderId as string);
			} else if (!newFolderId && currentWorkflowUuid.value !== "0") {
				// Reset to root if folderId is removed from URL
				currentWorkflowUuid.value = "0";
				breadcrumbs.value = [];
				loadProjects();
			}
		}
	);

	// Initialize based on the current route
	onMounted(() => {
		const folderId = route.params.folderId as string | undefined;
		if (folderId) {
			loadFolder(folderId);
		} else {
			loadProjects(); // Load root if no folderId
		}
	});

	const openFolder = (project: Project) => {
		if (project.type === "project") {
			// Update breadcrumbs
			breadcrumbs.value.push({ uuid: project.uuid, name: project.name });
			currentWorkflowUuid.value = project.uuid;

			// Update URL to reflect the opened folder
			router.push({
				name: "projects",
				params: { folderId: project.uuid },
			});
		}
	};

	let options = [
		{
			label: t("create-copy"),
			icon: DocumentDuplicateIcon,
			action: (project: Project) => {
				cloneProject(project.uuid)
					.then(() => {
						loadProjects();
					})
					.catch((error) => {
						console.error("克隆项目失败:", error);
						Error(t("create-copy-failed"), error || t("operation-failed"));
					});
			},
		},
		{
			label: t("move"),
			icon: ArrowRightIcon,
			action: (project: Project) => {
				moveTarget.value = project;
				showMoveDialog.value = true;
			},
		},
		{
			label: t("delete"),
			icon: TrashIcon,
			action: (project: Project) => {
				deleteTarget.value = project;
				showDeleteDialog.value = true;
			},
		},
	];

	const goBackRoot = () => {
		// Clear breadcrumbs and reset to root folder
		breadcrumbs.value = [];
		currentWorkflowUuid.value = "0";

		// Update URL to remove folderId
		router.push({ name: "projects" });
	};

	const navigateBreadcrumb = (idx: number) => {
		// Update breadcrumbs to include only up to the clicked index
		breadcrumbs.value = breadcrumbs.value.slice(0, idx + 1);

		// Get the UUID of the selected breadcrumb
		const targetUuid = breadcrumbs.value[idx]?.uuid;
		currentWorkflowUuid.value = targetUuid || "0";

		// Update URL to reflect the selected folder
		if (!targetUuid || targetUuid === "0") {
			router.push({ name: "projects" });
		} else {
			router.push({
				name: "projects",
				params: { folderId: targetUuid },
			});
		}
	};

	const createNewWorkflow = (example: string = "") => {
		router.push({
			name: "workflow",
			params: { id: "new" },
			query: {
				example,
				folderId:
					currentWorkflowUuid.value !== "0" ? currentWorkflowUuid.value : undefined,
			},
		});
	};

	// 删除确认对话框相关函数
	const closeDeleteDialog = () => {
		showDeleteDialog.value = false;
		deleteTarget.value = null;
	};

	const handleGroupCreated = () => {
		showCreateGroupDialog.value = false;
		loadProjects();
	};

	// 移动项目对话框相关函数
	const closeMoveDialog = () => {
		showMoveDialog.value = false;
		moveTarget.value = null;
	};

	const handleProjectMoved = () => {
		showMoveDialog.value = false;
		moveTarget.value = null;
		loadProjects();
	};

	// 编辑项目相关函数
	const handleEditProject = (project: Project) => {
		editTarget.value = project;
		showEditDialog.value = true;
	};

	const closeEditDialog = () => {
		showEditDialog.value = false;
		editTarget.value = null;
	};

	const handleProjectUpdated = () => {
		closeEditDialog();
		loadProjects();
	};

	// 分享管理相关函数
	const handleManageShares = (project: Project) => {
		shareTarget.value = project;
		showSharesManager.value = true;
	};

	const closeSharesManager = () => {
		showSharesManager.value = false;
		shareTarget.value = null;
	};

	const confirmDeleteProject = async () => {
		if (!deleteTarget.value) return;

		isDeleting.value = true;
		try {
			await deleteProject(deleteTarget.value.uuid);
			loadProjects();
			closeDeleteDialog();
		} catch (error) {
			console.error("删除项目失败:", error);
			Error(t("delete-failed"), t("operation-failed"));
		} finally {
			isDeleting.value = false;
		}
	};
</script>

<template>
	<div class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-full p-6">
		<div
			class="flex flex-col gap-2 md:flex-row md:justify-between md:items-center mb-6 box-border overflow-scroll md:overflow-auto"
		>
			<div
				class="flex-1 flex flex-col items-start justify-center text-center md:text-left"
			>
				<!-- 面包屑导航 -->
				<ProjectsBreadcrumb
					v-if="breadcrumbs.length > 0"
					:breadcrumbs="breadcrumbs"
					@go-back-root="goBackRoot"
					@navigate="navigateBreadcrumb"
				/>
				<h1 v-else class="text-2xl md:text-3xl font-bold text-gray-800 mt-2 md:mt-0">
					{{ t("projects") }}
				</h1>
			</div>

			<div
				class="flex gap-1 items-center md:flex-row md:gap-2 md:items-stretch box-border"
			>
				<button
					class="btn btn-xs md:btn-sm btn-primary inline-flex items-center justify-center w-auto px-4"
					@click="createNewWorkflow()"
				>
					<PlusIcon class="w-4 h-4 mr-1" />
					{{ t("create-workflow") }}
				</button>
				<button
					class="btn btn-xs md:btn-sm btn-secondary inline-flex items-center justify-center w-auto px-4"
					@click="showCreateGroupDialog = true"
				>
					<PlusIcon class="w-4 h-4 mr-1" />
					{{ t("new-project-group") }}
				</button>
			</div>
		</div>

		<!-- 加载状态 -->
		<ProjectsLoading v-if="loading" />

		<!-- 空状态 -->
		<ProjectsEmptyState
			v-else-if="projects.length === 0"
			@create-new-workflow="createNewWorkflow"
		/>

		<!-- 项目列表 -->
		<div
			v-else
			class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
		>
			<ProjectItem
				v-for="project in projects"
				:key="project.uuid"
				:project="project"
				:options="options"
				@open-folder="openFolder"
				@edit-project="handleEditProject"
				@manage-shares="handleManageShares"
			/>
		</div>

		<Page
			v-if="projects.length > 0"
			:page="page"
			:pageSize="pageSize"
			:total="total"
			@change="(e) => (page = e)"
			class="mt-8"
		></Page>

		<!-- 删除确认对话框 -->
		<DeleteConfirmDialog
			:show="showDeleteDialog"
			:item-name="deleteTarget?.name"
			:is-loading="isDeleting"
			@close="closeDeleteDialog"
			@confirm="confirmDeleteProject"
		/>

		<!-- 新建项目组对话框 -->
		<CreateProjectGroupDialog
			:show="showCreateGroupDialog"
			:current-folder-id="currentWorkflowUuid"
			@close="showCreateGroupDialog = false"
			@created="handleGroupCreated"
		/>

		<!-- 移动项目对话框 -->
		<MoveProjectDialog
			:show="showMoveDialog"
			:project="moveTarget"
			@close="closeMoveDialog"
			@moved="handleProjectMoved"
		/>

		<!-- 编辑项目对话框 -->
		<EditProjectDialog
			:show="showEditDialog"
			:project="editTarget"
			@close="closeEditDialog"
			@updated="handleProjectUpdated"
		/>

		<!-- 分享管理对话框 -->
		<SharesManager
			v-if="shareTarget"
			v-model="showSharesManager"
			:workflow-id="shareTarget.uuid"
			:workflow-name="shareTarget.name"
		/>
	</div>
</template>
