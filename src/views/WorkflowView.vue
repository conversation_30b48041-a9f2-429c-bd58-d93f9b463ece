<script setup lang="ts">
	import type { FlowExportObject } from "@vue-flow/core";
	import { ref, defineAsyncComponent, Teleport } from "vue";
	import { useRoute, useRouter } from "vue-router";
	import { useCurNav } from "@/stores/curNav";
	import component_wait from "@/components/utils/component_wait.vue";
	import { useNodeOptions } from "@/composables/useNodeOptions";
	import { useI18n } from "vue-i18n";
	import PublishModal from "@/components/workflow/PublishModal.vue";
	import ControlButtons from "@/components/workflow/ControlButtons.vue";
	import SharesManager from "@/components/shares/SharesManager.vue";
	import { useWorkflow } from "@/composables/useWorkflow";

	const { t } = useI18n();
	const router = useRouter();

	const Workflow = defineAsyncComponent(() => import("@/components/workflow.vue"));
	const Runtime = defineAsyncComponent(() => import("@/components/runtime.vue"));
	const Config = defineAsyncComponent(() => import("@/components/config.vue"));

	const flowId = ref(`vue-flow-${Date.now()}`);
	const curNavStore = useCurNav();
	curNavStore.setCurNav("");

	const workflowRef = ref();
	const {
		currentProjectID,
		saveModel,
		needSave,
		currentSavedData,
		projectName,
		projectDesc,
		saveLoading,
		loadLoading,
		isPublished,
		data,
		parentFolderId,
		onWorkflowSave,
		workflowSaveProcess,
		change,
	} = useWorkflow(workflowRef);

	const showPublishModal = ref(false);
	const showSharesManager = ref(false);

	// 新增 Config 相关状态管理
	const showConfig = ref(false);
	const configNodeID = ref("");
	const configNodeType = ref("");
	const configWorkflow = ref(flowId.value);

	const closeConfig = () => {
		showConfig.value = false;
		workflowRef.value?.focus();
	};

	const onNodeConfig = (id: string, type: string, subWorkflow?: any) => {
		configNodeID.value = id;
		configNodeType.value = type;
		configWorkflow.value = flowId.value;

		if (subWorkflow) {
			configWorkflow.value = subWorkflow;
		}

		showConfig.value = true;
	};

	const deleteNode = (id: string) => {
		workflowRef.value?.deleteNode(id);
	};

	// 运行时相关
	const runtimeData = ref<FlowExportObject>({
		nodes: [],
		edges: [],
		position: [0, 0],
		zoom: 1,
		viewport: { x: 0, y: 0, zoom: 1 },
	});
	const runtimeShow = ref(false);

	const handleRun = () => {
		const flowObject = workflowRef.value?.run();
		if (!flowObject) return;

		runtimeShow.value = true;
		runtimeData.value = flowObject;
	};

	const { options: nodeOptions } = useNodeOptions();

	const handlePublish = () => {
		const { flowObject } = workflowRef.value?.save() || {};
		if (flowObject) {
			currentSavedData.value = flowObject;
		}
		showPublishModal.value = true;
	};

	const handlePublishSuccess = () => {
		isPublished.value = true;
	};

	const handleShare = () => {
		showSharesManager.value = true;
	};
</script>

<template>
	<div class="w-full h-full">
		<!-- Loading 状态 -->
		<div
			class="w-full h-full flex justify-center items-center flex-col"
			v-if="loadLoading"
		>
			<span class="loading loading-ring loading-lg"></span>
			<span class="text-md text-center mt-3 text-gray-500">{{ t("loading") }}</span>
		</div>

		<!-- 主要内容 -->
		<div class="relative h-full" v-else>
			<ControlButtons
				:currentProjectID="currentProjectID"
				@return="
					router.push({
						name: 'projects',
						params: { folderId: parentFolderId },
					})
				"
				@debug="handleRun"
				@save="onWorkflowSave"
				@publish="handlePublish"
				@share="handleShare"
			/>

			<!-- Workflow 组件 -->
			<component_wait>
				<Workflow
					ref="workflowRef"
					:flowId="flowId"
					class="bg-base-100 p-4 rounded-lg h-full"
					@change="change"
					@node-config="onNodeConfig"
					:data="data"
					:key="currentProjectID"
					:options="nodeOptions"
				/>
			</component_wait>

			<!-- Runtime 组件 -->
			<Teleport to="body">
				<Runtime v-model="runtimeShow" :data="runtimeData" />
			</Teleport>

			<!-- Config 组件 -->
			<Config
				v-if="showConfig"
				@close="closeConfig"
				@delete="deleteNode"
				:id="configNodeID"
				:type="configNodeType"
				:flowId="configWorkflow"
			/>

			<!-- 保存对话框 -->
			<Teleport to="body">
				<div class="modal" :class="{ 'modal-open': saveModel }">
					<div
						class="modal-box relative md:max-w-[80%] md:w-[80%] lg:max-w-[60%] lg:w-[60%] max-w-full w-full"
					>
						<h3 class="font-bold text-xl">{{ t("save") }}</h3>
						<label>{{ t("project-name") }}</label>
						<input
							class="input input-bordered w-full input-md"
							v-model="projectName"
						/>
						<label>{{ t("project-description") }}</label>
						<textarea
							class="textarea textarea-bordered w-full"
							v-model="projectDesc"
						></textarea>
						<div class="modal-action">
							<button class="btn" @click="saveModel = false">
								{{ t("close") }}
							</button>
							<button
								class="btn btn-primary"
								:class="{ 'btn-disabled': saveLoading }"
								@click="workflowSaveProcess"
							>
								<span
									v-if="saveLoading"
									class="loading loading-spinner loading-sm"
								></span>
								{{ t("save") }}
							</button>
						</div>
					</div>

					<label class="modal-backdrop" @click="saveModel = false"></label>
				</div>
			</Teleport>

			<!-- 发布对话框 -->
			<PublishModal
				v-model="showPublishModal"
				:workflow-id="currentProjectID"
				:project-name="projectName"
				:project-desc="projectDesc"
				:is-published="isPublished"
				:flow-data="currentSavedData"
				@publish-success="handlePublishSuccess"
				@save-success="needSave = false"
			/>

			<!-- 分享管理对话框 -->
			<SharesManager
				v-model="showSharesManager"
				:workflow-id="currentProjectID"
				:workflow-name="projectName"
			/>
		</div>
	</div>
</template>

<style></style>
