<template>
	<div class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-full p-2 sm:p-6">
		<div
			class="w-full h-full flex justify-center items-center flex-col"
			v-if="loadLoading"
		>
			<span class="loading loading-ring loading-lg"></span>
			<span class="text-md text-center mt-3 text-gray-500">
				{{ t("loading") }}
			</span>
		</div>
		<div
			v-if="loadLoading == false"
			class="relative bg-white rounded-xl overflow-hidden shadow-md box-border min-h-full p-2 sm:p-5"
		>
			<h1 class="text-xl font-bold rounded-md inline-block"># {{ title }}</h1>
			<runtimeEngine
				:project_id="project_id"
				:data="data"
				mode="run"
				:key="project_id"
				:view_mode="viewMode"
			></runtimeEngine>
		</div>
	</div>
</template>
<script setup lang="ts">
	import { useRoute } from "vue-router";
	import { getProject } from "@/api/projects";
	import { ref, watch, defineAsyncComponent } from "vue";
	import { useCurNav } from "@/stores/curNav";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();

	const runtimeEngine = defineAsyncComponent(
		() => import("@/components/utils/runtime_engine.vue")
	);
	const curNavStore = useCurNav();
	curNavStore.setCurNav("");

	const route = useRoute();
	const data = ref({});
	const title = ref("");
	const loadLoading = ref(false);
	const viewMode = ref("default");
	const project_id = ref(route.params.id as string);

	const loadProjectInput = (id: string) => {
		loadLoading.value = true;
		getProject(id)
			.then((res) => {
				title.value = res.name;
				document.title = title.value + " - FlowAI";
				data.value = JSON.parse(res.data || "{}");
			})
			.finally(() => {
				loadLoading.value = false;
			});
	};

	watch(
		() => route.params.id as string,
		(val) => {
			project_id.value = val;
		}
	);

	watch(
		() => project_id.value,
		(val) => {
			loadProjectInput(val);
		}
	);

	loadProjectInput(project_id.value);
</script>
