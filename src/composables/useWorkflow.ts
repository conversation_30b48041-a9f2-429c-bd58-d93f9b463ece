import { ref, watch } from 'vue';
import { onBeforeRouteLeave, onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import type { FlowExportObject } from '@vue-flow/core';
import { createProject, updateProject, getProject, type Project } from '@/api/projects';
import { Error, Success } from '@/utils/notify';
import workflowExample from '@/utils/workflow_example';

export function useWorkflow(workflowRef: any) {
    const { t, locale } = useI18n();
    const router = useRouter();
    const route = useRoute();

    const currentProjectID = ref('');
    const saveModel = ref(false);
    const isInit = ref(false);
    const needSave = ref(false);
    const currentSavedData = ref<any>({});
    const projectName = ref('');
    const projectDesc = ref('');
    const saveLoading = ref(false);
    const loadLoading = ref(true);
    const isPublished = ref(false);
    const data = ref<FlowExportObject>();
    const parentFolderId = ref(route.query.folderId as string | undefined);

    const onWorkflowSave = () => {
        const { flowObject, hasOutputNode } = workflowRef.value?.save() || {};
        if (!flowObject) return;

        if (!hasOutputNode) {
            const confirmSave = confirm(t('current-workflow-has-no-output-node-confirm'));
            if (!confirmSave) return;
        }

        saveModel.value = true;
        currentSavedData.value = flowObject;
    };

    const workflowSaveProcess = () => {
        if (!projectName.value.trim()) {
            Error(t('error'), t('project-name-cannot-be-empty'));
            return;
        }

        const saveData = currentSavedData.value ? JSON.stringify(currentSavedData.value) : '{}';
        const parentId = route.query.folderId as string | undefined;
        let request: Project = {
            uuid: currentProjectID.value,
            name: projectName.value.trim(),
            description: projectDesc.value.trim(),
            data: saveData,
            created_at: '',
            parent_workflow_id: parentId || '0',
            type: 'workflow',
        };

        saveLoading.value = true;
        const promise = currentProjectID.value
            ? updateProject(currentProjectID.value, request)
            : createProject(request);

        promise
            .then((result: any) => {
                if (!currentProjectID.value) {
                    currentProjectID.value = result;
                    router.push({ name: 'workflow', params: { id: result } });
                }
                Success(t('success'), t('save-success'));
                needSave.value = false;
                saveModel.value = false;
            })
            .catch((err) => {
                Error(t('save-error'), err);
            })
            .finally(() => {
                saveLoading.value = false;
            });
    };

    const alertLeave = (event: any) => {
        if (needSave.value) {
            var message = t('content-not-saved-leave');
            event.preventDefault();
            event.returnValue = '';
            return message;
        }
    };

    const loadWorkflow = (id: string) => {
        getProject(id)
            .then((project) => {
                data.value = JSON.parse(project.data || '{}');
                projectName.value = project.name;
                projectDesc.value = project.description;
                isPublished.value = project.is_published || false;
                parentFolderId.value = project.parent_workflow_id;
            })
            .catch((err) => {
                Error(t('loading-error'), err);
            })
            .finally(() => {
                loadLoading.value = false;
            });
    };

    const init = (id: string, example: string = '') => {
        loadLoading.value = true;
        if (id == 'new') {
            currentProjectID.value = '';
            data.value = {} as FlowExportObject;
            projectName.value = '';
            projectDesc.value = '';
            needSave.value = false;
            loadLoading.value = false;
            if (example) {
                const isEnglish = locale.value === 'en';
                const exampleKey = `${example}${isEnglish ? '_En' : ''}` as keyof typeof workflowExample;
                if (workflowExample[exampleKey]) {
                    data.value = workflowExample[exampleKey] as FlowExportObject;
                }
            }
        } else {
            currentProjectID.value = id;
            loadWorkflow(id);
        }
        isInit.value = true;
    };

    window.addEventListener('beforeunload', alertLeave);

    onBeforeRouteLeave(() => {
        if (needSave.value) {
            const answer = window.confirm(t('content-not-saved-leave'));
            if (!answer) return false;
        }
        window.removeEventListener('beforeunload', alertLeave);
    });

    onBeforeRouteUpdate(() => {
        if (needSave.value) {
            const answer = window.confirm(t('content-not-saved-leave'));
            if (!answer) return false;
        }
        window.removeEventListener('beforeunload', alertLeave);
    });

    watch(() => route.params, (val) => {
        if (val.id) {
            init(val.id as string, route.query.example as string);
        }
    }, { immediate: true });

    const change = () => {
        if (isInit.value) {
            needSave.value = false;
            isInit.value = false;
            return;
        }
        needSave.value = true;
    };

    return {
        currentProjectID,
        saveModel,
        needSave,
        currentSavedData,
        projectName,
        projectDesc,
        saveLoading,
        loadLoading,
        isPublished,
        data,
        parentFolderId,
        onWorkflowSave,
        workflowSaveProcess,
        change,
    };
}
