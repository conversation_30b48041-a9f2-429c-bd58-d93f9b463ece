import type { User } from "@/api/user";
import { defineStore } from "pinia";
import { ref } from "vue";
import { identifyUserWithClarity, clearClarityIdentity } from "@/utils/clarity";

export const useUserStore = defineStore("user", () => {
	const userRef = ref<User>();

	function setUser(user: User) {
		userRef.value = user;
		// 在设置用户信息时自动调用 Clarity 用户标识
		identifyUserWithClarity(user);
	}

	function clearUser() {
		userRef.value = undefined;
		// 清除 Clarity 用户标识
		clearClarityIdentity();
	}

	return { userRef, setUser, clearUser };
});
