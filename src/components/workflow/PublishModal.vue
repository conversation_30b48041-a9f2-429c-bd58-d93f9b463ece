<script setup lang="ts">
	import { ref, computed, watch } from "vue";
	import { useI18n } from "vue-i18n";
	import { Error, Success } from "@/utils/notify";
	import { publishWorkflow, deletePublishedWorkflow } from "@/api/publish";
	import type { PublishedVersion } from "@/api/projects";
	import { publishedVersions, updateProject, type Project } from "@/api/projects";
	import { XMarkIcon } from "@heroicons/vue/24/outline";
	const { t } = useI18n();

	const props = defineProps<{
		modelValue: boolean;
		workflowId: string;
		projectName: string;
		projectDesc: string;
		isPublished: boolean;
		flowData?: any;
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: boolean): void;
		(e: "publish-success"): void;
		(e: "save-success"): void;
	}>();

	const show = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	const publishLoading = ref(false);
	const saveLoading = ref(false);
	const loadingPublishedVersion = ref(false);
	const publishedVersionList = ref<PublishedVersion[]>([]);
	const deleteVersionLoading = ref(false);

	const loadVersions = async () => {
		loadingPublishedVersion.value = true;
		try {
			const res = await publishedVersions(props.workflowId);
			publishedVersionList.value = (res.data?.list as PublishedVersion[]) || [];
		} catch (err) {
			console.error(err);
			Error(t("error"), t("failed-to-load-published-version"));
		} finally {
			loadingPublishedVersion.value = false;
		}
	};

	const handleDeleteVersion = async (versionId: string) => {
		if (!confirm(t("confirm-delete-version"))) return;

		try {
			deleteVersionLoading.value = true;
			await deletePublishedWorkflow(versionId);
			Success(t("success"), t("delete-version-success"));
			await loadVersions();
		} catch (err) {
			Error(t("error"), t("delete-version-error"));
		} finally {
			deleteVersionLoading.value = false;
		}
	};

	const saveWorkflow = async () => {
		if (!props.projectName.trim()) {
			Error(t("error"), t("project-name-cannot-be-empty"));
			return false;
		}

		const saveData = props.flowData ? JSON.stringify(props.flowData) : "{}";
		const request: Project = {
			uuid: props.workflowId,
			name: props.projectName.trim(),
			description: props.projectDesc.trim(),
			data: saveData,
			// 下面这些不重要，不会被保存
			created_at: "",
			parent_workflow_id: "0",
			type: "workflow",
		};

		try {
			saveLoading.value = true;
			await updateProject(props.workflowId, request);
			Success(t("success"), t("save-success"));
			emit("save-success");
			return true;
		} catch (err) {
			Error(t("save-error"), err as string);
			return false;
		} finally {
			saveLoading.value = false;
		}
	};

	const publishWorkflowProcess = async () => {
		if (!confirm(t("confirm-save-and-publish"))) return;

		try {
			publishLoading.value = true;

			const saveResult = await saveWorkflow();
			if (!saveResult) {
				publishLoading.value = false;
				return;
			}

			await publishWorkflow(
				props.workflowId,
				props.projectName,
				"latest",
				props.projectDesc
			);
			Success(t("success"), t("publish-success"));
			emit("publish-success");
			await loadVersions();
		} catch (err) {
			Error(t("publish-error"), err as string);
		} finally {
			publishLoading.value = false;
		}
	};

	watch(
		() => show.value,
		(newVal) => {
			if (newVal) {
				loadVersions();
			}
		}
	);
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': show }">
			<div class="modal-box relative max-w-2xl">
				<div class="flex flex-col space-y-6">
					<div class="flex justify-between items-center">
						<h3 class="font-bold text-2xl">{{ t("publish-workflow") }}</h3>
						<button class="btn btn-ghost btn-circle btn-sm" @click="show = false">
							<XMarkIcon class="w-5 h-5" />
						</button>
					</div>

					<div class="flex flex-col space-y-4">
						<div>
							<p class="text-base-content/70 mb-2">
								{{ t("publish-warning-message") }}
							</p>
							<button
								class="btn btn-primary w-full"
								:class="{ 'btn-disabled': publishLoading || saveLoading }"
								@click="publishWorkflowProcess"
							>
								<span
									v-if="publishLoading || saveLoading"
									class="loading loading-spinner loading-sm mr-2"
								></span>
								{{ t("save-and-publish-current-workflow") }}
							</button>
						</div>
					</div>

					<div class="mt-4">
						<div class="flex items-center justify-between mb-4">
							<h4 class="font-semibold text-lg">
								{{ t("published-versions") }}
							</h4>
							<span class="text-sm text-base-content/70"
								>{{ publishedVersionList.length }} {{ t("versions") }}</span
							>
						</div>

						<div class="bg-base-200 rounded-lg p-4">
							<div
								v-if="loadingPublishedVersion"
								class="flex items-center justify-center py-8"
							>
								<span class="loading loading-spinner loading-md"></span>
							</div>
							<div
								v-else-if="publishedVersionList.length === 0"
								class="flex flex-col items-center justify-center py-8 text-base-content/70"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-12 w-12 mb-4"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
									/>
								</svg>
								<p class="text-lg font-medium">
									{{ t("no-published-versions") }}
								</p>
							</div>
							<div v-else class="space-y-3">
								<div
									v-for="version in publishedVersionList"
									:key="version.id"
									class="flex items-center justify-between p-3 bg-base-100 rounded-lg border border-base-100 hover:border-primary transition-colors"
								>
									<div class="flex items-center space-x-3">
										<span class="badge badge-primary">{{
											version.version
										}}</span>
										<span class="text-sm text-base-content/70">{{
											version.updated_at
										}}</span>
									</div>
									<div class="flex items-center space-x-3">
										<span
											class="w-2 h-2 rounded-full"
											:class="
												version.is_active ? 'bg-success' : 'bg-error'
											"
										></span>
										<button
											class="btn btn-error btn-sm"
											:class="{ 'btn-disabled': deleteVersionLoading }"
											@click="handleDeleteVersion(version.id)"
										>
											<span
												v-if="deleteVersionLoading"
												class="loading loading-spinner loading-sm mr-2"
											></span>
											{{ t("delete") }}
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<label class="modal-backdrop" @click="show = false"></label>
		</div>
	</Teleport>
</template>
