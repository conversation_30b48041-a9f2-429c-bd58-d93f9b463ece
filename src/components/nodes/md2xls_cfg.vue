<template>
	<div v-if="nodeData">
		<p class="mt-4">
			{{ t("node_name") }}: <NodeName :nodeId="prop.id" v-model="nodeData.data.name" />
		</p>

		<p class="mt-4">{{t('nodes.md2xls.md_content')}}:</p>
        <select
            class="select select-sm select-bordered w-full"
            v-model="nodeData.data.input.md_content"
        >
            <option disabled selected value="">--{{ t("pls-select") }}--</option>
            <option v-for="i in vars" :value="'$' + i.node + '.' + i.name" :key="i.node + '.' + i.name">
                ${{ i.node + "." + i.name }}
            </option>
        </select>

		<div class="divider"></div>
		<p>{{ t("output") }}:</p>
		<div
			v-for="i in nodeData.data.output"
			:key="i.name"
			class="mt-2 px-5 py-3 rounded-lg bg-slate-50 font-thin border-l-2 border-slate-200"
		>
			<span class="font-bold block">${{ nodeData.data.name }}.{{ i.name }}</span>
			<p class="m-0 text-xs font-thin">{{ i.desc }}</p>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { useNodesData } from "@vue-flow/core";
	import { ref } from "vue";
	import getLinkedVar from "@/utils/linkedNodeVars";
	import NodeName from "@/components/utils/node_name_input.vue";
	import { useI18n } from "vue-i18n";

	const { t } = useI18n();
	const prop = defineProps(["id"]);
	const nodeData = useNodesData(prop.id);
	const vars = ref(getLinkedVar(prop.id));

</script>
