<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import type { ShareRateLimit } from "@/api/shares";
import { ExclamationTriangleIcon, CheckCircleIcon } from "@heroicons/vue/24/outline";

const { t } = useI18n();

const props = defineProps<{
	rateLimit: ShareRateLimit;
}>();

const isEnabled = computed(() => props.rateLimit.enabled);

const formatTime = (seconds: number) => {
	if (seconds < 60) return `${seconds}s`;
	if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
	return `${Math.floor(seconds / 3600)}h`;
};

const getLimitColor = (limit: number, used: number) => {
	const percentage = (used / limit) * 100;
	if (percentage >= 90) return "text-error";
	if (percentage >= 70) return "text-warning";
	return "text-success";
};

const getProgressColor = (limit: number, used: number) => {
	const percentage = (used / limit) * 100;
	if (percentage >= 90) return "progress-error";
	if (percentage >= 70) return "progress-warning";
	return "progress-success";
};
</script>

<template>
	<div class="space-y-4">
		<div v-if="!isEnabled" class="alert alert-success">
			<CheckCircleIcon class="w-6 h-6" />
			<div>
				<h4 class="font-bold">{{ t("shares.rate_limiting_disabled") }}</h4>
				<p class="text-sm">{{ t("shares.no_usage_limits") }}</p>
			</div>
		</div>

		<div v-else>
			<div class="alert alert-info">
				<ExclamationTriangleIcon class="w-6 h-6" />
				<div>
					<h4 class="font-bold">{{ t("shares.rate_limiting_enabled") }}</h4>
					<p class="text-sm">{{ t("shares.usage_limits_apply") }}</p>
				</div>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
				<div class="bg-base-200 rounded-lg p-4">
					<h5 class="font-semibold mb-2">{{ t("shares.daily_limits") }}</h5>
					<div class="space-y-2">
						<div class="flex justify-between items-center">
							<span class="text-sm">{{ t("shares.total_daily_runs") }}:</span>
							<span class="font-mono text-sm">{{ rateLimit.daily_run_limit }}</span>
						</div>
						<div class="flex justify-between items-center">
							<span class="text-sm">{{ t("shares.per_ip_daily_runs") }}:</span>
							<span class="font-mono text-sm">{{ rateLimit.per_ip_daily_limit }}</span>
						</div>
					</div>
				</div>

				<div class="bg-base-200 rounded-lg p-4">
					<h5 class="font-semibold mb-2">{{ t("shares.time_limits") }}</h5>
					<div class="space-y-2">
						<div class="flex justify-between items-center">
							<span class="text-sm">{{ t("shares.hourly_runs") }}:</span>
							<span class="font-mono text-sm">{{ rateLimit.hourly_run_limit }}</span>
						</div>
						<div class="flex justify-between items-center">
							<span class="text-sm">{{ t("shares.cooldown_period") }}:</span>
							<span class="font-mono text-sm">{{ formatTime(rateLimit.cooldown_seconds) }}</span>
						</div>
					</div>
				</div>
			</div>

			<div class="bg-base-200 rounded-lg p-4 mt-4">
				<p class="text-sm text-base-content/70">
					{{ t("shares.rate_limit_explanation") }}
				</p>
			</div>
		</div>
	</div>
</template>