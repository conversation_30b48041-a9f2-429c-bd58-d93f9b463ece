<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { Error } from "@/utils/notify";
import type { SharedWorkflowInfo } from "@/api/shares";
import { ArrowLeftIcon, EyeIcon, ChartBarIcon } from "@heroicons/vue/24/outline";

const { t } = useI18n();

const props = defineProps<{
	workflowInfo: SharedWorkflowInfo;
}>();

const emit = defineEmits<{
	(e: "back"): void;
}>();

const showGraph = computed(() => {
	return props.workflowInfo.share_info.ui_config.show_workflow_graph;
});

const workflowData = computed(() => {
	try {
		return typeof props.workflowInfo.data === 'string' 
			? JSON.parse(props.workflowInfo.data) 
			: props.workflowInfo.data;
	} catch (e) {
		console.error('Failed to parse workflow data:', e);
		return null;
	}
});

const nodeCount = computed(() => {
	if (!workflowData.value?.nodes) return 0;
	return workflowData.value.nodes.length;
});

const edgeCount = computed(() => {
	if (!workflowData.value?.edges) return 0;
	return workflowData.value.edges.length;
});

const handleBack = () => {
	emit("back");
};
</script>

<template>
	<div class="max-w-6xl mx-auto">
		<!-- Header -->
		<div class="card bg-base-100 shadow-lg mb-6">
			<div class="card-body">
				<div class="flex items-center justify-between">
					<div class="flex items-center space-x-4">
						<button class="btn btn-ghost btn-circle" @click="handleBack">
							<ArrowLeftIcon class="w-5 h-5" />
						</button>
						<div>
							<h1 class="text-2xl font-bold flex items-center gap-2">
								<EyeIcon class="w-6 h-6" />
								{{ workflowInfo.name }}
							</h1>
							<p class="text-base-content/70">
								{{ workflowInfo.description || t("shares.no_description") }}
							</p>
						</div>
					</div>
					<div class="text-right">
						<div class="text-sm text-base-content/60">
							{{ t("shares.by") }} {{ workflowInfo.owner }}
						</div>
						<div class="text-sm text-base-content/60">
							{{ t("shares.shared_as") }}: {{ workflowInfo.share_info.title }}
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Workflow Statistics -->
		<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
			<div class="stat bg-base-100 rounded-lg shadow">
				<div class="stat-figure text-primary">
					<ChartBarIcon class="w-8 h-8" />
				</div>
				<div class="stat-title">{{ t("shares.nodes") }}</div>
				<div class="stat-value text-primary">{{ nodeCount }}</div>
			</div>
			<div class="stat bg-base-100 rounded-lg shadow">
				<div class="stat-figure text-secondary">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-8 h-8 stroke-current">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
				</div>
				<div class="stat-title">{{ t("shares.connections") }}</div>
				<div class="stat-value text-secondary">{{ edgeCount }}</div>
			</div>
			<div class="stat bg-base-100 rounded-lg shadow">
				<div class="stat-figure text-accent">
					<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-8 h-8 stroke-current">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
					</svg>
				</div>
				<div class="stat-title">{{ t("shares.complexity") }}</div>
				<div class="stat-value text-accent">{{ nodeCount > 10 ? t("shares.high") : nodeCount > 5 ? t("shares.medium") : t("shares.low") }}</div>
			</div>
		</div>

		<!-- Workflow Graph (if enabled) -->
		<div v-if="showGraph" class="card bg-base-100 shadow-lg mb-6">
			<div class="card-body">
				<h2 class="card-title mb-4">{{ t("shares.workflow_structure") }}</h2>
				<div class="bg-base-200 rounded-lg p-8 min-h-96 flex items-center justify-center">
					<div class="text-center">
						<ChartBarIcon class="w-16 h-16 mx-auto mb-4 text-base-content/50" />
						<p class="text-lg text-base-content/70">{{ t("shares.workflow_graph_placeholder") }}</p>
						<p class="text-sm text-base-content/50 mt-2">
							{{ t("shares.graph_would_show_here") }}
						</p>
					</div>
				</div>
			</div>
		</div>

		<!-- Workflow Description -->
		<div class="card bg-base-100 shadow-lg mb-6">
			<div class="card-body">
				<h2 class="card-title mb-4">{{ t("shares.about_this_workflow") }}</h2>
				<div class="prose max-w-none">
					<p>{{ workflowInfo.description || t("shares.no_description_provided") }}</p>
				</div>

				<!-- Share Info -->
				<div class="mt-6 p-4 bg-base-200 rounded-lg">
					<h3 class="font-semibold mb-2">{{ t("shares.share_details") }}</h3>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
						<div>
							<span class="font-medium">{{ t("shares.share_title") }}:</span>
							<span class="ml-2">{{ workflowInfo.share_info.title }}</span>
						</div>
						<div>
							<span class="font-medium">{{ t("shares.billing_method") }}:</span>
							<span class="ml-2">
								{{ workflowInfo.share_info.bill_to === "sharer" 
									? t("shares.free_to_use") 
									: t("shares.pay_to_use") 
								}}
							</span>
						</div>
					</div>
					<div v-if="workflowInfo.share_info.description" class="mt-2">
						<span class="font-medium">{{ t("shares.share_description") }}:</span>
						<p class="mt-1 text-base-content/70">{{ workflowInfo.share_info.description }}</p>
					</div>
				</div>
			</div>
		</div>

		<!-- Actions -->
		<div class="card bg-base-100 shadow-lg">
			<div class="card-body">
				<div class="flex justify-between items-center">
					<h2 class="card-title">{{ t("shares.ready_to_proceed") }}</h2>
					<button class="btn btn-primary" @click="handleBack">
						{{ t("shares.back_to_actions") }}
					</button>
				</div>
			</div>
		</div>
	</div>
</template>