<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import type { ShareRateLimit } from "@/api/shares";

const { t } = useI18n();

const props = defineProps<{
	modelValue: ShareRateLimit;
}>();

const emit = defineEmits<{
	(e: "update:modelValue", value: ShareRateLimit): void;
}>();

const rateLimit = computed({
	get: () => props.modelValue,
	set: (value) => emit("update:modelValue", value),
});

const updateField = (field: keyof ShareRateLimit, value: any) => {
	rateLimit.value = {
		...rateLimit.value,
		[field]: value,
	};
};

const formatTime = (seconds: number) => {
	if (seconds < 60) return `${seconds}s`;
	if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
	return `${Math.floor(seconds / 3600)}h`;
};
</script>

<template>
	<div class="space-y-4">
		<!-- Enable Rate Limiting -->
		<div class="form-control">
			<label class="label cursor-pointer">
				<span class="label-text">{{ t("shares.enable_rate_limiting") }}</span>
				<input
					type="checkbox"
					class="toggle toggle-primary"
					:checked="rateLimit.enabled"
					@change="updateField('enabled', ($event.target as HTMLInputElement).checked)"
				/>
			</label>
			<label class="label">
				<span class="label-text-alt">{{ t("shares.rate_limiting_desc") }}</span>
			</label>
		</div>

		<!-- Rate Limiting Settings -->
		<div v-if="rateLimit.enabled" class="space-y-4 pl-4 border-l-2 border-primary/20">
			<!-- Daily Run Limit -->
			<div class="form-control">
				<label class="label">
					<span class="label-text">{{ t("shares.daily_run_limit") }}</span>
				</label>
				<input
					type="number"
					class="input input-bordered w-full"
					min="0"
					max="10000"
					:value="rateLimit.daily_run_limit"
					@input="updateField('daily_run_limit', parseInt(($event.target as HTMLInputElement).value) || 0)"
				/>
				<label class="label">
					<span class="label-text-alt">{{ t("shares.daily_run_limit_desc") }}</span>
				</label>
			</div>

			<!-- Hourly Run Limit -->
			<div class="form-control">
				<label class="label">
					<span class="label-text">{{ t("shares.hourly_run_limit") }}</span>
				</label>
				<input
					type="number"
					class="input input-bordered w-full"
					min="0"
					max="1000"
					:value="rateLimit.hourly_run_limit"
					@input="updateField('hourly_run_limit', parseInt(($event.target as HTMLInputElement).value) || 0)"
				/>
				<label class="label">
					<span class="label-text-alt">{{ t("shares.hourly_run_limit_desc") }}</span>
				</label>
			</div>

			<!-- Per IP Daily Limit -->
			<div class="form-control">
				<label class="label">
					<span class="label-text">{{ t("shares.per_ip_daily_limit") }}</span>
				</label>
				<input
					type="number"
					class="input input-bordered w-full"
					min="0"
					max="1000"
					:value="rateLimit.per_ip_daily_limit"
					@input="updateField('per_ip_daily_limit', parseInt(($event.target as HTMLInputElement).value) || 0)"
				/>
				<label class="label">
					<span class="label-text-alt">{{ t("shares.per_ip_daily_limit_desc") }}</span>
				</label>
			</div>

			<!-- Cooldown Seconds -->
			<div class="form-control">
				<label class="label">
					<span class="label-text">{{ t("shares.cooldown_seconds") }}</span>
				</label>
				<div class="flex items-center space-x-2">
					<input
						type="number"
						class="input input-bordered flex-1"
						min="0"
						max="3600"
						:value="rateLimit.cooldown_seconds"
						@input="updateField('cooldown_seconds', parseInt(($event.target as HTMLInputElement).value) || 0)"
					/>
					<span class="text-sm text-base-content/70">
						{{ formatTime(rateLimit.cooldown_seconds) }}
					</span>
				</div>
				<label class="label">
					<span class="label-text-alt">{{ t("shares.cooldown_seconds_desc") }}</span>
				</label>
			</div>

			<!-- Rate Limiting Tips -->
			<div class="alert alert-info">
				<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
				</svg>
				<div>
					<h4 class="font-bold">{{ t("shares.rate_limiting_tips") }}</h4>
					<ul class="text-sm mt-2 space-y-1">
						<li>• {{ t("shares.rate_limit_tip_1") }}</li>
						<li>• {{ t("shares.rate_limit_tip_2") }}</li>
						<li>• {{ t("shares.rate_limit_tip_3") }}</li>
					</ul>
				</div>
			</div>
		</div>

		<!-- Rate Limiting Disabled Info -->
		<div v-else class="alert alert-warning">
			<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L8.732 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
			</svg>
			<div>
				<h4 class="font-bold">{{ t("shares.rate_limiting_disabled") }}</h4>
				<p class="text-sm">{{ t("shares.rate_limiting_disabled_desc") }}</p>
			</div>
		</div>
	</div>
</template>