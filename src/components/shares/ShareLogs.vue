<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useI18n } from "vue-i18n";
import { Error } from "@/utils/notify";
import { SharesAPI, type WorkflowShare, type ShareLog } from "@/api/shares";
import { XMarkIcon, EyeIcon, PlayIcon, DocumentDuplicateIcon, ChevronLeftIcon, ChevronRightIcon } from "@heroicons/vue/24/outline";

const { t } = useI18n();

const props = defineProps<{
	modelValue: boolean;
	workflowId: string;
	share: WorkflowShare | null;
}>();

const emit = defineEmits<{
	(e: "update:modelValue", value: boolean): void;
}>();

const show = computed({
	get: () => props.modelValue,
	set: (value) => emit("update:modelValue", value),
});

const logs = ref<ShareLog[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(20);
const totalLogs = ref(0);
const hasNext = ref(false);

const totalPages = computed(() => Math.ceil(totalLogs.value / pageSize.value));

const loadLogs = async (page: number = 1) => {
	if (!props.share) return;
	
	loading.value = true;
	try {
		const res = await SharesAPI.getShareLogs(props.workflowId, props.share.share_uuid, page, pageSize.value);
		logs.value = res.logs;
		currentPage.value = res.pagination.page;
		totalLogs.value = res.pagination.total;
		hasNext.value = res.pagination.has_next;
	} catch (err) {
		console.error(err);
		Error(t("error"), t("shares.failed_to_load_logs"));
	} finally {
		loading.value = false;
	}
};

const handlePageChange = (page: number) => {
	if (page >= 1 && page <= totalPages.value) {
		loadLogs(page);
	}
};

const formatDate = (dateString: string) => {
	return new Date(dateString).toLocaleString();
};

const getActionIcon = (action: string) => {
	switch (action) {
		case "view":
			return EyeIcon;
		case "run":
			return PlayIcon;
		case "copy":
			return DocumentDuplicateIcon;
		default:
			return EyeIcon;
	}
};

const getActionText = (action: string) => {
	switch (action) {
		case "view":
			return t("shares.viewed");
		case "run":
			return t("shares.executed");
		case "copy":
			return t("shares.copied");
		default:
			return action;
	}
};

const getActionColor = (action: string) => {
	switch (action) {
		case "view":
			return "text-info";
		case "run":
			return "text-success";
		case "copy":
			return "text-warning";
		default:
			return "text-base-content";
	}
};

const getUserAgent = (userAgent: string) => {
	// Simple user agent parsing
	if (userAgent.includes("Chrome")) return "Chrome";
	if (userAgent.includes("Firefox")) return "Firefox";
	if (userAgent.includes("Safari")) return "Safari";
	if (userAgent.includes("Edge")) return "Edge";
	return "Other";
};

const getIPLocation = (ip: string) => {
	// This is a placeholder - in a real implementation you might want to use a geolocation service
	return ip;
};

// Watch for share changes and reload logs
watch(
	() => props.share,
	() => {
		if (props.share) {
			currentPage.value = 1;
			loadLogs();
		}
	},
	{ immediate: true }
);

// Watch for modal open/close
watch(
	() => show.value,
	(newVal) => {
		if (newVal && props.share) {
			loadLogs();
		}
	}
);
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': show }">
			<div class="modal-box relative max-w-6xl">
				<div class="flex flex-col space-y-6" v-if="share">
					<div class="flex justify-between items-center">
						<h3 class="font-bold text-2xl">
							{{ t("shares.access_logs") }}
						</h3>
						<button class="btn btn-ghost btn-circle btn-sm" @click="show = false">
							<XMarkIcon class="w-5 h-5" />
						</button>
					</div>

					<!-- Share Info -->
					<div class="bg-base-200 rounded-lg p-4">
						<h4 class="font-semibold mb-2">{{ share.title }}</h4>
						<p class="text-base-content/70 text-sm">
							{{ t("shares.showing_logs_for", { share: share.title }) }}
						</p>
					</div>

					<!-- Logs Content -->
					<div class="bg-base-100 rounded-lg border border-base-300">
						<div class="card-body">
							<div class="flex justify-between items-center mb-4">
								<h4 class="font-semibold">{{ t("shares.recent_activity") }}</h4>
								<div class="text-sm text-base-content/70">
									{{ t("shares.total_logs", { count: totalLogs }) }}
								</div>
							</div>

							<div
								v-if="loading"
								class="flex items-center justify-center py-8"
							>
								<span class="loading loading-spinner loading-md"></span>
							</div>
							<div
								v-else-if="logs.length === 0"
								class="flex flex-col items-center justify-center py-8 text-base-content/70"
							>
								<DocumentDuplicateIcon class="w-12 h-12 mb-4" />
								<p class="text-lg font-medium">
									{{ t("shares.no_logs") }}
								</p>
								<p class="text-sm mt-2">
									{{ t("shares.no_logs_desc") }}
								</p>
							</div>
							<div v-else class="space-y-2">
								<div
									v-for="log in logs"
									:key="log.id"
									class="flex items-center justify-between p-3 bg-base-200 rounded-lg hover:bg-base-300 transition-colors"
								>
									<div class="flex items-center space-x-3">
										<div class="flex items-center space-x-2">
											<component 
												:is="getActionIcon(log.action)" 
												class="w-5 h-5"
												:class="getActionColor(log.action)"
											/>
											<span class="font-medium" :class="getActionColor(log.action)">
												{{ getActionText(log.action) }}
											</span>
										</div>
										<div class="text-sm text-base-content/70">
											{{ formatDate(log.accessed_at) }}
										</div>
									</div>
									<div class="flex items-center space-x-4 text-sm">
										<div class="flex items-center space-x-2">
											<span class="font-medium">IP:</span>
											<span class="font-mono">{{ getIPLocation(log.visitor_ip) }}</span>
										</div>
										<div class="flex items-center space-x-2">
											<span class="font-medium">{{ t("shares.browser") }}:</span>
											<span>{{ getUserAgent(log.visitor_user_agent) }}</span>
										</div>
									</div>
								</div>
							</div>

							<!-- Pagination -->
							<div v-if="totalPages > 1" class="flex justify-center items-center space-x-2 mt-6">
								<button
									class="btn btn-sm btn-outline"
									:disabled="currentPage <= 1"
									@click="handlePageChange(currentPage - 1)"
								>
									<ChevronLeftIcon class="w-4 h-4" />
									{{ t("shares.previous") }}
								</button>
								
								<div class="flex space-x-1">
									<button
										v-for="page in Math.min(totalPages, 5)"
										:key="page"
										class="btn btn-sm"
										:class="page === currentPage ? 'btn-primary' : 'btn-outline'"
										@click="handlePageChange(page)"
									>
										{{ page }}
									</button>
								</div>
								
								<button
									class="btn btn-sm btn-outline"
									:disabled="currentPage >= totalPages"
									@click="handlePageChange(currentPage + 1)"
								>
									{{ t("shares.next") }}
									<ChevronRightIcon class="w-4 h-4" />
								</button>
							</div>
						</div>
					</div>

					<!-- Actions -->
					<div class="flex justify-end">
						<button class="btn btn-primary" @click="show = false">
							{{ t("shares.close") }}
						</button>
					</div>
				</div>
			</div>
			<label class="modal-backdrop" @click="show = false"></label>
		</div>
	</Teleport>
</template>