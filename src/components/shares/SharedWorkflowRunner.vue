<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { Error, Success } from "@/utils/notify";
import { PublicSharesAPI, type SharedWorkflowInfo } from "@/api/shares";
import { ArrowLeftIcon, PlayIcon, StopIcon } from "@heroicons/vue/24/outline";

const { t } = useI18n();

const props = defineProps<{
	workflowInfo: SharedWorkflowInfo;
}>();

const emit = defineEmits<{
	(e: "back"): void;
}>();

const isRunning = ref(false);
const runResult = ref<string>("");
const inputData = ref<Record<string, any>>({});
const eventSource = ref<EventSource | null>(null);

const canRun = computed(() => {
	return props.workflowInfo.share_info.ui_config.allow_run;
});

const needsAuth = computed(() => {
	return props.workflowInfo.share_info.bill_to === "runner";
});

const hasRateLimit = computed(() => {
	return props.workflowInfo.share_info.rate_limit.enabled;
});

const rateLimitStatus = computed(() => {
	return props.workflowInfo.share_info.rate_limit_status;
});

const canExecute = computed(() => {
	if (!canRun.value) return false;
	if (needsAuth.value) {
		// Check if user is logged in (you might want to add proper auth state management)
		return false; // For now, assume not logged in
	}
	if (hasRateLimit.value && rateLimitStatus.value) {
		return (
			rateLimitStatus.value.remaining.daily > 0 &&
			rateLimitStatus.value.remaining.hourly > 0 &&
			rateLimitStatus.value.remaining.per_ip_daily > 0 &&
			rateLimitStatus.value.remaining.cooldown_remaining <= 0
		);
	}
	return true;
});

const getExecutionBlockReason = computed(() => {
	if (!canRun.value) return t("shares.execution_not_allowed");
	if (needsAuth.value) return t("shares.login_required");
	if (hasRateLimit.value && rateLimitStatus.value) {
		const status = rateLimitStatus.value;
		if (status.remaining.daily <= 0) return t("shares.daily_limit_reached");
		if (status.remaining.hourly <= 0) return t("shares.hourly_limit_reached");
		if (status.remaining.per_ip_daily <= 0) return t("shares.ip_limit_reached");
		if (status.remaining.cooldown_remaining > 0) {
			return t("shares.cooldown_active", { seconds: status.remaining.cooldown_remaining });
		}
	}
	return null;
});

const handleBack = () => {
	if (isRunning.value) {
		stopExecution();
	}
	emit("back");
};

const startExecution = async () => {
	if (!canExecute.value) return;

	try {
		isRunning.value = true;
		runResult.value = "";

		const response = await PublicSharesAPI.executeSharedWorkflow(
			props.workflowInfo.share_info.rate_limit_status?.enabled ? 
				props.workflowInfo.uuid : 
				props.workflowInfo.uuid, 
			{ input: inputData.value }
		);

		if (!response.body) {
			throw new Error("No response body");
		}

		const reader = response.body.getReader();
		const decoder = new TextDecoder();

		while (true) {
			const { done, value } = await reader.read();
			if (done) break;

			const chunk = decoder.decode(value);
			const lines = chunk.split('\n');

			for (const line of lines) {
				if (line.trim()) {
					if (line.startsWith('data: ')) {
						const data = line.substring(6);
						try {
							const parsed = JSON.parse(data);
							handleStreamData(parsed);
						} catch (e) {
							runResult.value += data + '\n';
						}
					}
				}
			}
		}

		Success(t("success"), t("shares.execution_completed"));
	} catch (err) {
		console.error(err);
		Error(t("error"), err as string);
	} finally {
		isRunning.value = false;
	}
};

const stopExecution = () => {
	if (eventSource.value) {
		eventSource.value.close();
		eventSource.value = null;
	}
	isRunning.value = false;
};

const handleStreamData = (data: any) => {
	// Handle different types of streaming data
	if (data.type === 'log') {
		runResult.value += data.content + '\n';
	} else if (data.type === 'result') {
		runResult.value += '\n--- Result ---\n' + JSON.stringify(data.content, null, 2) + '\n';
	} else if (data.type === 'error') {
		runResult.value += '\n--- Error ---\n' + data.content + '\n';
	} else {
		runResult.value += JSON.stringify(data) + '\n';
	}
};

const clearResult = () => {
	runResult.value = "";
};

// Initialize input data based on workflow
onMounted(() => {
	// You might want to extract input fields from workflow data
	inputData.value = {};
});
</script>

<template>
	<div class="max-w-6xl mx-auto">
		<!-- Header -->
		<div class="card bg-base-100 shadow-lg mb-6">
			<div class="card-body">
				<div class="flex items-center justify-between">
					<div class="flex items-center space-x-4">
						<button class="btn btn-ghost btn-circle" @click="handleBack">
							<ArrowLeftIcon class="w-5 h-5" />
						</button>
						<div>
							<h1 class="text-2xl font-bold flex items-center gap-2">
								<PlayIcon class="w-6 h-6" />
								{{ t("shares.run_workflow") }}
							</h1>
							<p class="text-base-content/70">{{ workflowInfo.name }}</p>
						</div>
					</div>
					<div class="text-right">
						<div class="text-sm text-base-content/60">
							{{ t("shares.shared_by") }} {{ workflowInfo.owner }}
						</div>
						<div class="text-sm text-base-content/60">
							{{ workflowInfo.share_info.title }}
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Rate Limit Status -->
		<div v-if="hasRateLimit && rateLimitStatus" class="card bg-base-100 shadow-lg mb-6">
			<div class="card-body">
				<h2 class="card-title mb-4">{{ t("shares.execution_limits") }}</h2>
				<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div class="stat bg-base-200 rounded-lg">
						<div class="stat-title">{{ t("shares.daily_remaining") }}</div>
						<div class="stat-value" :class="rateLimitStatus.remaining.daily > 0 ? 'text-success' : 'text-error'">
							{{ rateLimitStatus.remaining.daily }}
						</div>
						<div class="stat-desc">of {{ rateLimitStatus.limits.daily_run_limit }}</div>
					</div>
					<div class="stat bg-base-200 rounded-lg">
						<div class="stat-title">{{ t("shares.hourly_remaining") }}</div>
						<div class="stat-value" :class="rateLimitStatus.remaining.hourly > 0 ? 'text-success' : 'text-error'">
							{{ rateLimitStatus.remaining.hourly }}
						</div>
						<div class="stat-desc">of {{ rateLimitStatus.limits.hourly_run_limit }}</div>
					</div>
					<div class="stat bg-base-200 rounded-lg">
						<div class="stat-title">{{ t("shares.cooldown") }}</div>
						<div class="stat-value" :class="rateLimitStatus.remaining.cooldown_remaining > 0 ? 'text-warning' : 'text-success'">
							{{ rateLimitStatus.remaining.cooldown_remaining }}s
						</div>
						<div class="stat-desc">{{ t("shares.until_next_run") }}</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Execution Block Warning -->
		<div v-if="!canExecute" class="alert alert-warning mb-6">
			<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L8.732 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
			</svg>
			<div>
				<h3 class="font-bold">{{ t("shares.execution_blocked") }}</h3>
				<div class="text-sm">{{ getExecutionBlockReason }}</div>
			</div>
		</div>

		<!-- Input Section -->
		<div class="card bg-base-100 shadow-lg mb-6">
			<div class="card-body">
				<h2 class="card-title mb-4">{{ t("shares.workflow_input") }}</h2>
				<div class="form-control">
					<label class="label">
						<span class="label-text">{{ t("shares.input_data") }}</span>
					</label>
					<textarea
						v-model="inputData"
						class="textarea textarea-bordered h-32"
						:placeholder="t('shares.input_placeholder')"
						:disabled="isRunning"
					></textarea>
					<label class="label">
						<span class="label-text-alt">{{ t("shares.input_description") }}</span>
					</label>
				</div>
			</div>
		</div>

		<!-- Execution Controls -->
		<div class="card bg-base-100 shadow-lg mb-6">
			<div class="card-body">
				<div class="flex justify-between items-center">
					<h2 class="card-title">{{ t("shares.execution_controls") }}</h2>
					<div class="flex space-x-2">
						<button
							v-if="runResult"
							class="btn btn-ghost btn-sm"
							@click="clearResult"
							:disabled="isRunning"
						>
							{{ t("shares.clear_result") }}
						</button>
						<button
							v-if="isRunning"
							class="btn btn-error"
							@click="stopExecution"
						>
							<StopIcon class="w-4 h-4 mr-2" />
							{{ t("shares.stop_execution") }}
						</button>
						<button
							v-else
							class="btn btn-primary"
							@click="startExecution"
							:disabled="!canExecute"
						>
							<PlayIcon class="w-4 h-4 mr-2" />
							{{ t("shares.start_execution") }}
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Output Section -->
		<div class="card bg-base-100 shadow-lg">
			<div class="card-body">
				<h2 class="card-title mb-4">{{ t("shares.execution_output") }}</h2>
				<div class="bg-base-200 rounded-lg p-4 min-h-96">
					<div v-if="isRunning" class="flex items-center space-x-2 mb-4">
						<span class="loading loading-spinner loading-sm"></span>
						<span class="text-sm">{{ t("shares.executing") }}</span>
					</div>
					<pre v-if="runResult" class="text-sm whitespace-pre-wrap">{{ runResult }}</pre>
					<div v-else class="flex items-center justify-center h-80 text-base-content/50">
						<div class="text-center">
							<PlayIcon class="w-12 h-12 mx-auto mb-2" />
							<p>{{ t("shares.no_output_yet") }}</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>