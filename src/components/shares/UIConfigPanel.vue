<script setup lang="ts">
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import type { ShareUIConfig } from "@/api/shares";
import { EyeIcon, PlayIcon, DocumentDuplicateIcon, ChartBarIcon } from "@heroicons/vue/24/outline";

const { t } = useI18n();

const props = defineProps<{
	modelValue: ShareUIConfig;
}>();

const emit = defineEmits<{
	(e: "update:modelValue", value: ShareUIConfig): void;
}>();

const uiConfig = computed({
	get: () => props.modelValue,
	set: (value) => emit("update:modelValue", value),
});

const updateField = (field: keyof ShareUIConfig, value: any) => {
	uiConfig.value = {
		...uiConfig.value,
		[field]: value,
	};
};

const hasAnyPermission = computed(() => {
	return uiConfig.value.allow_view || uiConfig.value.allow_run || uiConfig.value.allow_copy;
});
</script>

<template>
	<div class="space-y-4">
		<!-- Permissions -->
		<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
			<!-- Allow View -->
			<div class="form-control">
				<label class="label cursor-pointer">
					<span class="label-text flex items-center gap-2">
						<EyeIcon class="w-4 h-4" />
						{{ t("shares.allow_view") }}
					</span>
					<input
						type="checkbox"
						class="toggle toggle-primary"
						:checked="uiConfig.allow_view"
						@change="updateField('allow_view', ($event.target as HTMLInputElement).checked)"
					/>
				</label>
				<label class="label">
					<span class="label-text-alt">{{ t("shares.allow_view_desc") }}</span>
				</label>
			</div>

			<!-- Allow Run -->
			<div class="form-control">
				<label class="label cursor-pointer">
					<span class="label-text flex items-center gap-2">
						<PlayIcon class="w-4 h-4" />
						{{ t("shares.allow_run") }}
					</span>
					<input
						type="checkbox"
						class="toggle toggle-primary"
						:checked="uiConfig.allow_run"
						@change="updateField('allow_run', ($event.target as HTMLInputElement).checked)"
					/>
				</label>
				<label class="label">
					<span class="label-text-alt">{{ t("shares.allow_run_desc") }}</span>
				</label>
			</div>

			<!-- Allow Copy -->
			<div class="form-control">
				<label class="label cursor-pointer">
					<span class="label-text flex items-center gap-2">
						<DocumentDuplicateIcon class="w-4 h-4" />
						{{ t("shares.allow_copy") }}
					</span>
					<input
						type="checkbox"
						class="toggle toggle-primary"
						:checked="uiConfig.allow_copy"
						@change="updateField('allow_copy', ($event.target as HTMLInputElement).checked)"
					/>
				</label>
				<label class="label">
					<span class="label-text-alt">{{ t("shares.allow_copy_desc") }}</span>
				</label>
			</div>
		</div>

		<!-- Permission Validation Alert -->
		<div v-if="!hasAnyPermission" class="alert alert-error">
			<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
			</svg>
			<span>{{ t("shares.at_least_one_permission") }}</span>
		</div>

		<!-- Display Options -->
		<div class="form-control">
			<label class="label cursor-pointer">
				<span class="label-text flex items-center gap-2">
					<ChartBarIcon class="w-4 h-4" />
					{{ t("shares.show_workflow_graph") }}
				</span>
				<input
					type="checkbox"
					class="toggle toggle-primary"
					:checked="uiConfig.show_workflow_graph"
					@change="updateField('show_workflow_graph', ($event.target as HTMLInputElement).checked)"
				/>
			</label>
			<label class="label">
				<span class="label-text-alt">{{ t("shares.show_workflow_graph_desc") }}</span>
			</label>
		</div>

		<!-- Welcome Message -->
		<div class="form-control">
			<label class="label">
				<span class="label-text">{{ t("shares.welcome_message") }}</span>
			</label>
			<textarea
				:value="uiConfig.welcome_message"
				@input="updateField('welcome_message', ($event.target as HTMLTextAreaElement).value)"
				class="textarea textarea-bordered w-full"
				:placeholder="t('shares.welcome_message_placeholder')"
				rows="3"
			></textarea>
			<label class="label">
				<span class="label-text-alt">{{ t("shares.welcome_message_desc") }}</span>
			</label>
		</div>

		<!-- Custom Theme (Advanced) -->
		<div class="collapse collapse-arrow bg-base-200">
			<input type="checkbox" class="peer" />
			<div class="collapse-title text-lg font-medium">
				{{ t("shares.custom_theme") }}
			</div>
			<div class="collapse-content">
				<div class="form-control">
					<label class="label">
						<span class="label-text">{{ t("shares.custom_theme_json") }}</span>
					</label>
					<textarea
						:value="JSON.stringify(uiConfig.custom_theme, null, 2)"
						@input="
							try {
								updateField('custom_theme', JSON.parse(($event.target as HTMLTextAreaElement).value));
							} catch (e) {
								// Invalid JSON, ignore
							}
						"
						class="textarea textarea-bordered w-full font-mono text-sm"
						:placeholder="t('shares.custom_theme_placeholder')"
						rows="6"
					></textarea>
					<label class="label">
						<span class="label-text-alt">{{ t("shares.custom_theme_desc") }}</span>
					</label>
				</div>
			</div>
		</div>

		<!-- UI Configuration Tips -->
		<div class="alert alert-info">
			<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
			</svg>
			<div>
				<h4 class="font-bold">{{ t("shares.ui_config_tips") }}</h4>
				<ul class="text-sm mt-2 space-y-1">
					<li>• {{ t("shares.ui_config_tip_1") }}</li>
					<li>• {{ t("shares.ui_config_tip_2") }}</li>
					<li>• {{ t("shares.ui_config_tip_3") }}</li>
				</ul>
			</div>
		</div>
	</div>
</template>