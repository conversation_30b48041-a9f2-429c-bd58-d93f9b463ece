<script setup lang="ts">
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import { Error, Success } from "@/utils/notify";
import { SharesAPI, type CreateShareRequest, DEFAULT_RATE_LIMIT, DEFAULT_UI_CONFIG } from "@/api/shares";
import { XMarkIcon } from "@heroicons/vue/24/outline";
import RateLimitConfig from "./RateLimitConfig.vue";
import UIConfigPanel from "./UIConfigPanel.vue";

const { t } = useI18n();

const props = defineProps<{
	modelValue: boolean;
	workflowId: string;
	workflowName: string;
}>();

const emit = defineEmits<{
	(e: "update:modelValue", value: boolean): void;
	(e: "create-success"): void;
}>();

const show = computed({
	get: () => props.modelValue,
	set: (value) => emit("update:modelValue", value),
});

const loading = ref(false);
const title = ref("");
const description = ref("");
const billTo = ref<"sharer" | "runner">("sharer");
const expiresAt = ref<string>("");
const rateLimit = ref({ ...DEFAULT_RATE_LIMIT });
const uiConfig = ref({ ...DEFAULT_UI_CONFIG });

const resetForm = () => {
	title.value = "";
	description.value = "";
	billTo.value = "sharer";
	expiresAt.value = "";
	rateLimit.value = { ...DEFAULT_RATE_LIMIT };
	uiConfig.value = { ...DEFAULT_UI_CONFIG };
};

const handleCreate = async () => {
	if (!title.value.trim()) {
		Error(t("error"), t("shares.title_required"));
		return;
	}

	if (!uiConfig.value.allow_view && !uiConfig.value.allow_run && !uiConfig.value.allow_copy) {
		Error(t("error"), t("shares.at_least_one_permission"));
		return;
	}

	const createData: CreateShareRequest = {
		title: title.value.trim(),
		description: description.value.trim(),
		bill_to: billTo.value,
		rate_limit: rateLimit.value,
		ui_config: uiConfig.value,
	};

	if (expiresAt.value) {
		const expiryDate = new Date(expiresAt.value);
		if (expiryDate <= new Date()) {
			Error(t("error"), t("shares.expiry_date_future"));
			return;
		}
		createData.expires_at = Math.floor(expiryDate.getTime() / 1000);
	}

	try {
		loading.value = true;
		await SharesAPI.createShare(props.workflowId, createData);
		Success(t("success"), t("shares.create_share_success"));
		resetForm();
		emit("create-success");
	} catch (err) {
		Error(t("error"), t("shares.create_share_error"));
	} finally {
		loading.value = false;
	}
};

const handleCancel = () => {
	resetForm();
	show.value = false;
};

// Format date for input[type="datetime-local"]
const formatDateTimeLocal = (date: Date) => {
	return date.toISOString().slice(0, 16);
};

// Set minimum date to current time
const minDateTime = computed(() => {
	return formatDateTimeLocal(new Date());
});
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': show }">
			<div class="modal-box relative max-w-4xl">
				<div class="flex flex-col space-y-6">
					<div class="flex justify-between items-center">
						<h3 class="font-bold text-2xl">{{ t("shares.create_share") }}</h3>
						<button class="btn btn-ghost btn-circle btn-sm" @click="handleCancel">
							<XMarkIcon class="w-5 h-5" />
						</button>
					</div>

					<div class="flex flex-col space-y-4">
						<!-- Basic Information -->
						<div class="card bg-base-100 border border-base-300">
							<div class="card-body">
								<h4 class="card-title text-lg">{{ t("shares.basic_info") }}</h4>
								
								<div class="form-control">
									<label class="label">
										<span class="label-text">{{ t("shares.share_title") }} *</span>
									</label>
									<input
										v-model="title"
										type="text"
										class="input input-bordered w-full"
										:placeholder="t('shares.title_placeholder')"
									/>
								</div>

								<div class="form-control">
									<label class="label">
										<span class="label-text">{{ t("shares.share_description") }}</span>
									</label>
									<textarea
										v-model="description"
										class="textarea textarea-bordered w-full"
										:placeholder="t('shares.description_placeholder')"
										rows="3"
									></textarea>
								</div>

								<div class="form-control">
									<label class="label">
										<span class="label-text">{{ t("shares.billing_method") }}</span>
									</label>
									<select v-model="billTo" class="select select-bordered w-full">
										<option value="sharer">{{ t("shares.sharer_pays") }}</option>
										<option value="runner">{{ t("shares.runner_pays") }}</option>
									</select>
									<label class="label">
										<span class="label-text-alt">
											{{ billTo === "sharer" ? t("shares.sharer_pays_desc") : t("shares.runner_pays_desc") }}
										</span>
									</label>
								</div>

								<div class="form-control">
									<label class="label">
										<span class="label-text">{{ t("shares.expires_at") }}</span>
									</label>
									<input
										v-model="expiresAt"
										type="datetime-local"
										class="input input-bordered w-full"
										:min="minDateTime"
									/>
									<label class="label">
										<span class="label-text-alt">{{ t("shares.expires_at_desc") }}</span>
									</label>
								</div>
							</div>
						</div>

						<!-- UI Configuration -->
						<div class="card bg-base-100 border border-base-300">
							<div class="card-body">
								<h4 class="card-title text-lg">{{ t("shares.ui_configuration") }}</h4>
								<UIConfigPanel v-model="uiConfig" />
							</div>
						</div>

						<!-- Rate Limiting -->
						<div class="card bg-base-100 border border-base-300">
							<div class="card-body">
								<h4 class="card-title text-lg">{{ t("shares.rate_limiting") }}</h4>
								<RateLimitConfig v-model="rateLimit" />
							</div>
						</div>

						<!-- Actions -->
						<div class="flex justify-end space-x-2">
							<button
								class="btn btn-ghost"
								@click="handleCancel"
								:disabled="loading"
							>
								{{ t("shares.cancel") }}
							</button>
							<button
								class="btn btn-primary"
								@click="handleCreate"
								:disabled="loading"
							>
								<span
									v-if="loading"
									class="loading loading-spinner loading-sm mr-2"
								></span>
								{{ t("shares.create") }}
							</button>
						</div>
					</div>
				</div>
			</div>
			<label class="modal-backdrop" @click="handleCancel"></label>
		</div>
	</Teleport>
</template>