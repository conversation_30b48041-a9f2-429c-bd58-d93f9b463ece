<script setup lang="ts">
	import { computed } from "vue";
	import { useI18n } from "vue-i18n";
	import type { WorkflowShare, ShareStats as ShareStatsType } from "@/api/shares";
	import {
		XMarkIcon,
		EyeIcon,
		PlayIcon,
		DocumentDuplicateIcon,
		ChartBarIcon,
	} from "@heroicons/vue/24/outline";

	const { t } = useI18n();

	const props = defineProps<{
		modelValue: boolean;
		share: WorkflowShare | null;
		stats: ShareStatsType | null;
	}>();

	const emit = defineEmits<{
		(e: "update:modelValue", value: boolean): void;
	}>();

	const show = computed({
		get: () => props.modelValue,
		set: (value) => emit("update:modelValue", value),
	});

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleString();
	};

	const isExpired = (share: WorkflowShare) => {
		if (!share.expires_at) return false;
		return new Date(share.expires_at) < new Date();
	};

	const getStatusColor = (share: WorkflowShare) => {
		if (!share.is_enabled) return "text-error";
		if (isExpired(share)) return "text-warning";
		return "text-success";
	};

	const getStatusText = (share: WorkflowShare) => {
		if (!share.is_enabled) return t("shares.disabled");
		if (isExpired(share)) return t("shares.expired");
		return t("shares.active");
	};

	const totalInteractions = computed(() => {
		if (!props.stats) return 0;
		return props.stats.views + props.stats.runs + props.stats.copies;
	});
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': show }">
			<div class="modal-box relative max-w-4xl">
				<div class="flex flex-col space-y-6" v-if="share && stats">
					<div class="flex justify-between items-center">
						<h3 class="font-bold text-2xl flex items-center gap-2">
							<ChartBarIcon class="w-6 h-6" />
							{{ t("shares.share_statistics") }}
						</h3>
						<button class="btn btn-ghost btn-circle btn-sm" @click="show = false">
							<XMarkIcon class="w-5 h-5" />
						</button>
					</div>

					<!-- Share Basic Info -->
					<div class="card bg-base-100 border border-base-300">
						<div class="card-body">
							<h4 class="card-title text-lg">{{ t("shares.share_details") }}</h4>
							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<h5 class="font-semibold text-lg mb-2">
										{{ share.title }}
									</h5>
									<p class="text-base-content/70 mb-4">
										{{ share.description || t("shares.no_description") }}
									</p>
									<div class="space-y-2 text-sm">
										<div class="flex justify-between">
											<span>{{ t("shares.status") }}:</span>
											<span
												:class="getStatusColor(share)"
												class="font-semibold"
											>
												{{ getStatusText(share) }}
											</span>
										</div>
										<div class="flex justify-between">
											<span>{{ t("shares.billing_method") }}:</span>
											<span>{{
												share.bill_to === "sharer"
													? t("shares.sharer_pays")
													: t("shares.runner_pays")
											}}</span>
										</div>
										<div class="flex justify-between">
											<span>{{ t("shares.created_at") }}:</span>
											<span>{{ formatDate(share.created_at) }}</span>
										</div>
										<div
											v-if="share.expires_at"
											class="flex justify-between"
										>
											<span>{{ t("shares.expires_at") }}:</span>
											<span>{{ formatDate(share.expires_at) }}</span>
										</div>
									</div>
								</div>
								<div>
									<h5 class="font-semibold mb-2">
										{{ t("shares.permissions") }}
									</h5>
									<div class="space-y-2">
										<div class="flex items-center gap-2">
											<EyeIcon class="w-4 h-4" />
											<span>{{ t("shares.view") }}:</span>
											<span
												:class="
													share.ui_config.allow_view
														? 'text-success'
														: 'text-error'
												"
											>
												{{
													share.ui_config.allow_view
														? t("shares.allowed")
														: t("shares.disabled")
												}}
											</span>
										</div>
										<div class="flex items-center gap-2">
											<PlayIcon class="w-4 h-4" />
											<span>{{ t("shares.run") }}:</span>
											<span
												:class="
													share.ui_config.allow_run
														? 'text-success'
														: 'text-error'
												"
											>
												{{
													share.ui_config.allow_run
														? t("shares.allowed")
														: t("shares.disabled")
												}}
											</span>
										</div>
										<div class="flex items-center gap-2">
											<DocumentDuplicateIcon class="w-4 h-4" />
											<span>{{ t("shares.copy") }}:</span>
											<span
												:class="
													share.ui_config.allow_copy
														? 'text-success'
														: 'text-error'
												"
											>
												{{
													share.ui_config.allow_copy
														? t("shares.allowed")
														: t("shares.disabled")
												}}
											</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Statistics -->
					<div class="card bg-base-100 border border-base-300">
						<div class="card-body">
							<h4 class="card-title text-lg">
								{{ t("shares.usage_statistics") }}
							</h4>

							<!-- Overview Stats -->
							<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
								<div class="stat bg-base-200 rounded-lg">
									<div class="stat-figure text-primary">
										<ChartBarIcon class="w-8 h-8" />
									</div>
									<div class="stat-title">
										{{ t("shares.total_interactions") }}
									</div>
									<div class="stat-value text-primary">
										{{ totalInteractions }}
									</div>
								</div>
								<div class="stat bg-base-200 rounded-lg">
									<div class="stat-figure text-info">
										<EyeIcon class="w-8 h-8" />
									</div>
									<div class="stat-title">{{ t("shares.total_views") }}</div>
									<div class="stat-value text-info">{{ stats.views }}</div>
								</div>
								<div class="stat bg-base-200 rounded-lg">
									<div class="stat-figure text-success">
										<PlayIcon class="w-8 h-8" />
									</div>
									<div class="stat-title">{{ t("shares.total_runs") }}</div>
									<div class="stat-value text-success">{{ stats.runs }}</div>
								</div>
								<div class="stat bg-base-200 rounded-lg">
									<div class="stat-figure text-warning">
										<DocumentDuplicateIcon class="w-8 h-8" />
									</div>
									<div class="stat-title">
										{{ t("shares.total_copies") }}
									</div>
									<div class="stat-value text-warning">
										{{ stats.copies }}
									</div>
								</div>
							</div>

							<!-- Engagement Metrics -->
							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div class="bg-base-200 rounded-lg p-4">
									<h5 class="font-semibold mb-2">
										{{ t("shares.engagement_rate") }}
									</h5>
									<div class="space-y-2">
										<div class="flex justify-between items-center">
											<span>{{ t("shares.view_to_run_rate") }}:</span>
											<span class="font-semibold">
												{{
													stats.views > 0
														? Math.round(
																(stats.runs / stats.views) *
																	100
														  )
														: 0
												}}%
											</span>
										</div>
										<div class="flex justify-between items-center">
											<span>{{ t("shares.view_to_copy_rate") }}:</span>
											<span class="font-semibold">
												{{
													stats.views > 0
														? Math.round(
																(stats.copies / stats.views) *
																	100
														  )
														: 0
												}}%
											</span>
										</div>
									</div>
								</div>
								<div class="bg-base-200 rounded-lg p-4">
									<h5 class="font-semibold mb-2">
										{{ t("shares.activity_breakdown") }}
									</h5>
									<div class="space-y-2">
										<div class="flex justify-between items-center">
											<span>{{ t("shares.views") }}:</span>
											<div class="flex items-center gap-2">
												<progress
													class="progress progress-info w-20"
													:value="stats.views"
													:max="totalInteractions"
												></progress>
												<span class="text-sm">{{ stats.views }}</span>
											</div>
										</div>
										<div class="flex justify-between items-center">
											<span>{{ t("shares.runs") }}:</span>
											<div class="flex items-center gap-2">
												<progress
													class="progress progress-success w-20"
													:value="stats.runs"
													:max="totalInteractions"
												></progress>
												<span class="text-sm">{{ stats.runs }}</span>
											</div>
										</div>
										<div class="flex justify-between items-center">
											<span>{{ t("shares.copies") }}:</span>
											<div class="flex items-center gap-2">
												<progress
													class="progress progress-warning w-20"
													:value="stats.copies"
													:max="totalInteractions"
												></progress>
												<span class="text-sm">{{ stats.copies }}</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Rate Limiting Info -->
					<div
						v-if="share.rate_limit.enabled"
						class="card bg-base-100 border border-base-300"
					>
						<div class="card-body">
							<h4 class="card-title text-lg">
								{{ t("shares.rate_limiting_info") }}
							</h4>
							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<h5 class="font-semibold mb-2">
										{{ t("shares.current_limits") }}
									</h5>
									<div class="space-y-2 text-sm">
										<div class="flex justify-between">
											<span>{{ t("shares.daily_limit") }}:</span>
											<span>{{ share.rate_limit.daily_run_limit }}</span>
										</div>
										<div class="flex justify-between">
											<span>{{ t("shares.hourly_limit") }}:</span>
											<span>{{
												share.rate_limit.hourly_run_limit
											}}</span>
										</div>
										<div class="flex justify-between">
											<span>{{ t("shares.per_ip_limit") }}:</span>
											<span>{{
												share.rate_limit.per_ip_daily_limit
											}}</span>
										</div>
										<div class="flex justify-between">
											<span>{{ t("shares.cooldown") }}:</span>
											<span
												>{{ share.rate_limit.cooldown_seconds }}s</span
											>
										</div>
									</div>
								</div>
								<div>
									<h5 class="font-semibold mb-2">
										{{ t("shares.protection_status") }}
									</h5>
									<p class="text-sm text-base-content/70">
										{{ t("shares.rate_limiting_active_desc") }}
									</p>
								</div>
							</div>
						</div>
					</div>

					<!-- Actions -->
					<div class="flex justify-end">
						<button class="btn btn-primary" @click="show = false">
							{{ t("shares.close") }}
						</button>
					</div>
				</div>
			</div>
			<label class="modal-backdrop" @click="show = false"></label>
		</div>
	</Teleport>
</template>
