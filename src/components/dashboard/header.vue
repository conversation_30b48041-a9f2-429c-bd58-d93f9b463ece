<template>
	<header
		class="bg-[#fbfcfd] py-2 px-6 flex items-center justify-between border-b border-slate-200 shadow-md"
	>
		<router-link :to="{ name: 'dashboard' }">
			<h1 class="text-xl font-bold text-neutral flex">
				<img class="w-8 h-8 block self-center shadow-sm rounded-lg" src="/icon.png" />
				<span
					class="self-center ml-2 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent"
					>FlowAI</span
				>
			</h1>
		</router-link>
		<div class="flex items-center space-x-4">
			<div
				v-if="userStore.userRef"
				class="hidden sm:flex items-center bg-gray-100 rounded-md px-3 py-1 text-sm cursor-pointer hover:bg-gray-200"
				@click="() => $router.push({ name: 'credits_history' })"
			>
				<svg
					width="20"
					height="20"
					viewBox="0 0 20 20"
					fill="none"
					class="mr-1"
					xmlns="http://www.w3.org/2000/svg"
				>
					<circle
						cx="10"
						cy="10"
						r="8"
						fill="#FFD54F"
						stroke="#F9A825"
						stroke-width="2"
					/>
					<text
						x="10"
						y="14"
						text-anchor="middle"
						font-size="10"
						fill="#fff"
						font-weight="bold"
						font-family="Arial, sans-serif"
					>
						P
					</text>
				</svg>
				{{ userStore.userRef.credits || 0 }}
			</div>
			<button class="btn btn-sm btn-modern" @click="triggerSearch">
				<MagnifyingGlassIcon class="w-4 h-4" />
			</button>
			<button class="btn btn-sm btn-modern" @click="createNewWorkflow">
				<DocumentPlusIcon class="w-4 h-4" /> {{ t("create-workflow") }}
			</button>
		</div>
	</header>
</template>

<script setup lang="ts">
	import { DocumentPlusIcon, MagnifyingGlassIcon } from "@heroicons/vue/24/outline";
	import { useRouter, useRoute } from "vue-router";
	import { useI18n } from "vue-i18n";
	import { useUserStore } from "@/stores/user";
	import { me } from "@/api/user";
	import { onMounted, onUnmounted, watch } from "vue";

	const $router = useRouter();
	const route = useRoute();
	const { t } = useI18n();
	const userStore = useUserStore();
	const triggerSearch = () => {
		const event = new KeyboardEvent("keydown", {
			key: "k",
			metaKey: true,
			bubbles: true,
		});
		document.dispatchEvent(event);
	};

	const createNewWorkflow = () => {
		$router.push({
			name: "workflow",
			params: { id: "new" },
			query: { folderId: route.params.folderId },
		});
	};

	// 路由跳转时自动刷新用户信息
	watch(() => route.fullPath, refreshUser);

	function refreshUser() {
		me()
			.then((res) => {
				userStore.setUser(res);
			})
			.catch((e) => {
				// ignore
			});
	}
	// 定时刷新用户信息（每60秒）
	let timer: number | undefined;
	onMounted(() => {
		timer = window.setInterval(refreshUser, 60000);
		refreshUser();
	});
	onUnmounted(() => {
		if (timer) clearInterval(timer);
	});
</script>

<style scoped></style>
