<template>
	<div class="relative group">
		<aside
			:class="[
				'bg-[#fbfcfd] border-r border-slate-200 transition-all duration-300',
				'sm:h-full sm:flex sm:flex-col sm:justify-between',
				'h-auto flex flex-row justify-start overflow-x-auto',
				{ 'sm:w-[60px]': isCollapsed, 'sm:w-[200px]': !isCollapsed },
				'w-full',
			]"
		>
			<ul
				class="p-2 flex flex-row sm:flex-col space-x-2 sm:space-x-0 w-full max-sm:justify-center"
			>
				<li v-for="i in menu" :key="i.id">
					<RouterLink
						:class="[
							'flex py-2 px-3 rounded-md transition-colors duration-200 items-center mb-1',
							props.current === i.id
								? 'bg-primary text-base-100'
								: 'hover:bg-primary hover:text-base-100',
							{ 'sm:justify-center': isCollapsed },
						]"
						:to="{ name: i.id }"
					>
						<component
							:is="i.icon"
							class="max-sm:mr-0"
							:class="[isCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-2']"
						></component>
						<span v-if="!isCollapsed" class="truncate hidden sm:inline">
							{{ i.name }}
						</span>
					</RouterLink>
				</li>
				<li class="sm:hidden self-center">
					<UserPop :isCollapsed="true" />
				</li>
			</ul>
			<UserPop :isCollapsed="isCollapsed" class="hidden sm:block"></UserPop>
		</aside>
		<button
			@click="toggleCollapse"
			class="absolute top-1/2 -right-1 transform -translate-y-1/2 bg-white border border-slate-200 rounded p-1 text-gray-500 hover:text-gray-700 opacity-0 group-hover:opacity-90 transition-opacity duration-200 hidden sm:block"
		>
			<component
				:is="isCollapsed ? ChevronRightIcon : ChevronLeftIcon"
				class="w-3 h-20"
			/>
		</button>
	</div>
</template>

<script setup lang="ts">
	import { ref, onMounted, onUnmounted, watch, defineAsyncComponent } from "vue";
	import {
		QueueListIcon,
		AdjustmentsHorizontalIcon,
		DocumentChartBarIcon,
		ChevronLeftIcon,
		ChevronRightIcon,
		SignalIcon,
	} from "@heroicons/vue/24/solid";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const UserPop = defineAsyncComponent(() => import("@/components/dashboard/user_pop.vue"));
	const props = defineProps({
		current: {
			type: String,
		},
	});

	const isCollapsed = ref(localStorage.getItem("menuCollapsed") === "true");

	const toggleCollapse = () => {
		isCollapsed.value = !isCollapsed.value;
		localStorage.setItem("menuCollapsed", isCollapsed.value.toString());
	};

	const updateCollapsedState = () => {
		if (window.innerWidth <= 640) {
			isCollapsed.value = false;
		} else {
			isCollapsed.value = localStorage.getItem("menuCollapsed") === "true";
		}
	};

	onMounted(() => {
		updateCollapsedState();
		window.addEventListener("resize", updateCollapsedState);
	});

	onUnmounted(() => {
		window.removeEventListener("resize", updateCollapsedState);
	});

	watch(isCollapsed, (newValue) => {
		if (window.innerWidth > 640) {
			localStorage.setItem("menuCollapsed", newValue.toString());
		}
	});

	const menu = ref([
		{ id: "projects", name: t("workflow"), icon: QueueListIcon, path: "/projects" },
		{
			id: "run_logs",
			name: t("run_logs"),
			icon: DocumentChartBarIcon,
			path: "/run_logs",
		},
		{
			id: "api_management",
			name: t("api_management"),
			icon: SignalIcon,
			path: "/api_management",
		},
		{
			id: "setting",
			name: t("settings"),
			icon: AdjustmentsHorizontalIcon,
			path: "/setting",
		},
	]);
</script>
