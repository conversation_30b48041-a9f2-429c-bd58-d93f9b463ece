<script setup lang="ts">
	import {
		DocumentPlusIcon,
		DocumentDuplicateIcon,
		RocketLaunchIcon,
	} from "@heroicons/vue/24/outline";
	import { useI18n } from "vue-i18n";

	const emit = defineEmits<{
		(e: "create-new-workflow", example?: string): void;
	}>();

	const { t } = useI18n();
</script>
<template>
	<div class="text-center">
		<div class="mx-auto">
			<div
				class="bg-white/80 backdrop-blur-sm rounded-3xl border border-slate-200 shadow-xl p-12"
			>
				<img src="@/assets/empty-projects.svg" class="mx-auto w-48 mb-8" />
				<div class="space-y-8">
					<div>
						<h2 class="text-3xl font-bold text-slate-800 mb-4">
							{{ t("no-projects") }}
						</h2>
						<p class="text-lg text-slate-600">
							{{ t("click-button-to-start-automation") }}
						</p>
					</div>
					<div>
						<button
							@click="emit('create-new-workflow')"
							class="btn-gradient-primary btn-lg gap-3"
						>
							<DocumentPlusIcon class="w-5 h-5" />
							{{ t("create-blank-workflow") }}
						</button>
					</div>
					<div class="border-t border-slate-200 pt-8 mt-8">
						<p class="text-lg text-slate-600 mb-8">
							{{ t("or-start-from-example") }}
						</p>
						<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
							<div
								class="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 py-6 px-4 border border-slate-200 hover:border-blue-300 hover:-translate-y-1 relative group"
							>
								<div
									class="absolute -top-3 left-4 px-3 py-1 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 text-xs rounded-full font-medium border border-blue-300"
								>
									{{ t("example") }}
								</div>
								<div class="flex items-center mb-3">
									<div
										class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center mr-3"
									>
										<DocumentDuplicateIcon class="w-5 h-5 text-blue-600" />
									</div>
									<h3
										class="text-lg font-bold text-slate-800 text-left break-all group-hover:text-blue-600 transition-colors"
									>
										{{ t("llm-data-processing") }}
									</h3>
								</div>
								<p
									class="text-sm text-slate-600 mb-4 text-left leading-relaxed"
								>
									{{ t("llm-data-processing-description") }}
								</p>
								<button
									@click="emit('create-new-workflow', 'llmDataProcessing')"
									class="btn-gradient-primary btn-sm w-full"
								>
									{{ t("use-this-example") }}
								</button>
							</div>

							<div
								class="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 py-6 px-4 border border-slate-200 hover:border-green-300 hover:-translate-y-1 relative group"
							>
								<div
									class="absolute -top-3 left-4 px-3 py-1 bg-gradient-to-r from-green-100 to-green-200 text-green-800 text-xs rounded-full font-medium border border-green-300"
								>
									{{ t("example") }}
								</div>
								<div class="flex items-center mb-3">
									<div
										class="w-10 h-10 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center mr-3"
									>
										<RocketLaunchIcon class="w-5 h-5 text-green-600" />
									</div>
									<h3
										class="text-lg font-bold text-slate-800 text-left break-all group-hover:text-green-600 transition-colors"
									>
										{{ t("multi-llm-assistant") }}
									</h3>
								</div>
								<p
									class="text-sm text-slate-600 mb-4 text-left leading-relaxed"
								>
									{{ t("integrate-multiple-llm-models") }}
								</p>
								<button
									@click="emit('create-new-workflow', 'multiLlmAssistant')"
									class="btn-gradient-success btn-sm w-full"
								>
									{{ t("use-this-example") }}
								</button>
							</div>

							<div
								class="bg-white/60 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 py-6 px-4 border border-slate-200 hover:border-purple-300 hover:-translate-y-1 relative group"
							>
								<div
									class="absolute -top-3 left-4 px-3 py-1 bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 text-xs rounded-full font-medium border border-purple-300"
								>
									{{ t("example") }}
								</div>
								<div class="flex items-center mb-3">
									<div
										class="w-10 h-10 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center mr-3"
									>
										<DocumentPlusIcon class="w-5 h-5 text-purple-600" />
									</div>
									<h3
										class="text-lg font-bold text-slate-800 text-left break-all group-hover:text-purple-600 transition-colors"
									>
										{{ t("logic-flow") }}
									</h3>
								</div>
								<p
									class="text-sm text-slate-600 mb-4 text-left leading-relaxed"
								>
									{{ t("visual-logic-flow") }}
								</p>
								<button
									@click="emit('create-new-workflow', 'logicFlow')"
									class="btn-gradient-purple btn-sm w-full"
								>
									{{ t("use-this-example") }}
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
