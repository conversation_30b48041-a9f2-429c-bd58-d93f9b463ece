<script setup lang="ts">
	import { ref, watch, computed } from "vue";
	import { useI18n } from "vue-i18n";
	import {
		XMarkIcon,
		FolderIcon,
		ChevronRightIcon,
		HomeIcon,
		ArrowPathIcon,
	} from "@heroicons/vue/24/outline";
	import { getProjectGroups, moveProject, type Project } from "@/api/projects";
	import { Success, Error } from "@/utils/notify";

	const props = defineProps<{
		show: boolean;
		project: Project | null;
	}>();

	const emit = defineEmits<{
		(e: "close"): void;
		(e: "moved"): void;
	}>();

	const { t } = useI18n();

	// 项目组数据
	const projectGroups = ref<Project[]>([]);
	const currentProjectGroups = ref<Project[]>([]);
	const selectedProjectGroup = ref<string>("0"); // '0' 表示根项目组
	const breadcrumbs = ref<{ id: string; name: string }[]>([]);
	const loading = ref(false);
	const moving = ref(false);

	// 加载项目组列表
	const loadProjectGroups = async (parentId: string = "0") => {
		loading.value = true;
		try {
			const res = await getProjectGroups(parentId);
			currentProjectGroups.value = res.list || [];
		} catch (error: any) {
			console.error("加载项目组失败:", error);
			Error(t("load-project-groups-failed"), error.message || t("operation-failed"));
		} finally {
			loading.value = false;
		}
	};

	// 进入项目组
	const enterProjectGroup = (projectGroup: Project) => {
		breadcrumbs.value.push({ id: projectGroup.uuid, name: projectGroup.name });
		selectedProjectGroup.value = projectGroup.uuid;
		loadProjectGroups(projectGroup.uuid);
	};

	// 返回根项目组
	const goToRoot = () => {
		breadcrumbs.value = [];
		selectedProjectGroup.value = "0";
		loadProjectGroups("0");
	};

	// 导航到面包屑指定位置
	const navigateToBreadcrumb = (index: number) => {
		if (index === -1) {
			goToRoot();
			return;
		}

		breadcrumbs.value = breadcrumbs.value.slice(0, index + 1);
		const target = breadcrumbs.value[index];
		selectedProjectGroup.value = target.id;
		loadProjectGroups(target.id);
	};

	// 确认移动
	const confirmMove = async () => {
		if (!props.project) return;

		// 检查是否选择了相同的项目组
		if (selectedProjectGroup.value === props.project.parent_workflow_id) {
			Error(t("move-to-same-project-group"), t("please-select-different-project-group"));
			return;
		}

		moving.value = true;
		try {
			await moveProject(props.project.uuid, selectedProjectGroup.value);
			Success(t("move-success"), t("project-moved-successfully"));
			emit("moved");
			emit("close");
		} catch (error: any) {
			console.error("移动项目失败:", error);
			Error(t("move-failed"), error.message || t("operation-failed"));
		} finally {
			moving.value = false;
		}
	};

	// 关闭对话框
	const closeDialog = () => {
		emit("close");
	};

	// 重置状态
	const resetState = () => {
		breadcrumbs.value = [];
		selectedProjectGroup.value = "0";
		currentProjectGroups.value = [];
	};

	// 当显示状态改变时
	watch(
		() => props.show,
		(show) => {
			if (show) {
				resetState();
				loadProjectGroups("0");
			}
		}
	);

	// 当前位置显示名称
	const currentLocationName = computed(() => {
		if (breadcrumbs.value.length === 0) {
			return t("root-directory");
		}
		return breadcrumbs.value[breadcrumbs.value.length - 1]?.name || "";
	});

	// 是否禁用移动按钮
	const isMoveDisabled = computed(() => {
		return (
			moving.value ||
			!props.project ||
			selectedProjectGroup.value === props.project.parent_workflow_id
		);
	});
</script>

<template>
	<Teleport to="body">
		<div
			v-if="show"
			class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
			@click.self="closeDialog"
		>

		<div
			class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[80vh] flex flex-col"
		>
			<!-- 头部 -->
			<div class="flex items-center justify-between p-6 border-b border-gray-200">
				<div class="flex items-center gap-3">
					<div
						class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center"
					>
						<FolderIcon class="w-6 h-6 text-blue-600" />
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900">
							{{ t("move-project") }}
						</h3>
						<p class="text-sm text-gray-500">
							{{ t("move-project-description", { name: project?.name || "" }) }}
						</p>
					</div>
				</div>
				<button
					@click="closeDialog"
					class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
				>
					<XMarkIcon class="w-5 h-5 text-gray-500" />
				</button>
			</div>

			<!-- 面包屑导航 -->
			<div class="px-6 py-4 border-b border-gray-100 bg-gray-50">
				<div class="flex items-center gap-2 text-sm flex-wrap">
					<button
						@click="goToRoot"
						class="flex items-center gap-1 px-2 py-1 rounded hover:bg-gray-200 transition-colors"
						:class="{ 'text-blue-600 font-medium': breadcrumbs.length === 0 }"
					>
						<HomeIcon class="w-4 h-4" />
						{{ t("root-directory") }}
					</button>

					<template v-for="(crumb, index) in breadcrumbs" :key="crumb.id">
						<ChevronRightIcon class="w-4 h-4 text-gray-400" />
						<button
							@click="navigateToBreadcrumb(index)"
							class="px-2 py-1 rounded hover:bg-gray-200 transition-colors"
							:class="{
								'text-blue-600 font-medium': index === breadcrumbs.length - 1,
							}"
						>
							{{ crumb.name }}
						</button>
					</template>
				</div>
			</div>

			<!-- 项目组列表 -->
			<div class="flex-1 overflow-y-auto p-6">
				<div v-if="loading" class="flex items-center justify-center py-12">
					<ArrowPathIcon class="w-6 h-6 text-gray-400 animate-spin mr-2" />
					<span class="text-gray-500">{{ t("loading") }}...</span>
				</div>

				<div v-else-if="currentProjectGroups.length === 0" class="text-center py-12">
					<FolderIcon class="w-12 h-12 text-gray-300 mx-auto mb-4" />
					<p class="text-gray-500">{{ t("no-sub-project-groups") }}</p>
				</div>

				<div v-else class="space-y-2">
					<div
						v-for="projectGroup in currentProjectGroups"
						:key="projectGroup.uuid"
						@click="projectGroup.uuid !== project?.uuid && enterProjectGroup(projectGroup)"
						class="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors group"
						:class="{
							'bg-blue-50 border border-blue-200':
								selectedProjectGroup === projectGroup.uuid,
							'opacity-50 cursor-not-allowed': projectGroup.uuid === project?.uuid,
						}"
					>
						<div
							class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center group-hover:bg-yellow-200 transition-colors"
						>
							<FolderIcon class="w-6 h-6 text-yellow-600" />
						</div>
						<div class="flex-1 min-w-0">
							<h4 class="font-medium text-gray-900 truncate">
								{{ projectGroup.name }}
							</h4>
							<p class="text-sm text-gray-500 truncate">
								{{ projectGroup.description || t("no-description") }}
							</p>
						</div>
						<ChevronRightIcon
							class="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors"
						/>
					</div>
				</div>
			</div>

			<!-- 当前选择显示 -->
			<div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
				<div class="flex items-center gap-2 text-sm">
					<span class="text-gray-600">{{ t("selected-destination") }}:</span>
					<span class="font-medium text-gray-900">{{ currentLocationName }}</span>
				</div>
			</div>

			<!-- 底部按钮 -->
			<div class="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
				<button
					@click="closeDialog"
					class="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
					:disabled="moving"
				>
					{{ t("cancel") }}
				</button>
				<button
					@click="confirmMove"
					:disabled="isMoveDisabled"
					class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
				>
					<ArrowPathIcon v-if="moving" class="w-4 h-4 animate-spin" />
					{{ moving ? t("moving") : t("move-here") }}
				</button>
			</div>
		</div>
		</div>
	</Teleport>
</template>
