<script setup lang="ts">
	import {
		RocketLaunchIcon,
		PencilSquareIcon,
		FolderIcon,
		EllipsisVerticalIcon,
		CalendarIcon,
	} from "@heroicons/vue/24/outline";
	import { useI18n } from "vue-i18n";
	import { ref, onMounted, onBeforeUnmount, computed } from "vue";
	import type { Project } from "@/api/projects";

	const props = defineProps<{
		project: Project;
		options: {
			label: string;
			icon: any;
			action: (project: Project) => void;
		}[];
	}>();

	const emit = defineEmits<{
		(e: "open-folder", project: Project): void;
		(e: "edit-project", project: Project): void;
	}>();

	const { t } = useI18n();

	const showDropdown = ref(false);

	const toggleDropdown = (event: MouseEvent) => {
		event.stopPropagation();
		showDropdown.value = !showDropdown.value;
	};

	const handleClickOutside = () => {
		if (showDropdown.value) {
			showDropdown.value = false;
		}
	};

	const formattedDate = computed(() => {
		if (!props.project.created_at) return "";
		return new Date(props.project.created_at).toLocaleDateString(undefined, {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	});

	const theme = computed(() => {
		if (props.project.type === "project") {
			return {
				hoverBorder: "hover:border-yellow-400",
				iconBg: "bg-yellow-100",
				iconColor: "text-yellow-600",
				groupHoverColor: "group-hover:text-yellow-700",
			};
		}
		// workflow
		return {
			hoverBorder: "hover:border-blue-300",
			iconBg: "bg-blue-100",
			iconColor: "text-blue-500",
			groupHoverColor: "group-hover:text-blue-600",
		};
	});

	onMounted(() => {
		document.addEventListener("click", handleClickOutside);
	});

	onBeforeUnmount(() => {
		document.removeEventListener("click", handleClickOutside);
	});
</script>

<template>
	<div
		:class="[
			'group flex flex-col bg-white rounded-2xl shadow-md transition-all duration-300 border border-slate-200 ',
			theme.hoverBorder,
		]"
		:data-project-id="project.uuid"
	>
		<div
			class="p-5 flex-grow"
			:class="project.type === 'project' ? 'cursor-pointer' : ''"
			@click="project.type === 'project' ? emit('open-folder', project) : null"
		>
			<div class="flex justify-between items-start mb-3">
				<div class="flex items-center gap-3 flex-1 min-w-0">
					<div
						:class="[
							'w-10 h-10 rounded-lg flex items-center justify-center',
							theme.iconBg,
						]"
					>
						<FolderIcon
							v-if="project.type === 'project'"
							:class="['w-6 h-6', theme.iconColor]"
						/>
						<PencilSquareIcon v-else :class="['w-6 h-6', theme.iconColor]" />
					</div>
					<div class="flex-1 min-w-0">
						<h2
							:class="[
								'text-lg font-bold text-slate-800 truncate transition-colors',
								theme.groupHoverColor,
							]"
							:title="project.name || t('unnamed-project')"
						>
							{{ project.name || t("unnamed-project") }}
						</h2>
						<div class="flex items-center text-xs text-slate-500 mt-1">
							<CalendarIcon class="h-4 w-4 mr-1.5 text-slate-400" />
							<span>{{ formattedDate }}</span>
						</div>
					</div>
				</div>

				<div class="relative ml-2">
					<button
						@click.stop="toggleDropdown"
						class="text-slate-400 hover:text-slate-600 p-2 rounded-full transition-colors hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
						aria-label="Options"
					>
						<EllipsisVerticalIcon class="w-5 h-5" />
					</button>
					<transition
						enter-active-class="transition ease-out duration-100"
						enter-from-class="transform opacity-0 scale-95"
						enter-to-class="transform opacity-100 scale-100"
						leave-active-class="transition ease-in duration-75"
						leave-from-class="transform opacity-100 scale-100"
						leave-to-class="transform opacity-0 scale-95"
					>
						<div
							v-if="showDropdown"
							class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg z-10 border border-slate-100"
						>
							<ul class="py-1">
								<li
									v-if="project.type === 'project'"
									@click.stop="
										emit('edit-project', project);
										showDropdown = false;
									"
									class="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 cursor-pointer transition-colors"
								>
									<PencilSquareIcon class="w-4 h-4 mr-3" />
									<span>{{ t("edit") }}</span>
								</li>
								<li
									v-for="option in options"
									:key="option.label"
									@click.stop="
										option.action(project);
										showDropdown = false;
									"
									class="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 cursor-pointer transition-colors"
								>
									<component :is="option.icon" class="w-4 h-4 mr-3" />
									<span>{{ option.label }}</span>
								</li>
							</ul>
						</div>
					</transition>
				</div>
			</div>

			<p
				class="text-sm text-slate-600 h-10 line-clamp-2"
				:title="project.description || ''"
			>
				{{ project.description || t("no-description") }}
			</p>
		</div>

		<div class="border-t border-slate-200 bg-slate-50/50 px-5 py-3">
			<div class="flex justify-end items-center gap-2">
				<template v-if="project.type === 'workflow'">
					<RouterLink
						:to="{ name: 'runtime', params: { id: project.uuid } }"
						class="btn-outline-primary btn-sm gap-2"
					>
						<RocketLaunchIcon class="w-4 h-4" />
						{{ t("run") }}
					</RouterLink>
					<RouterLink
						:to="{ name: 'workflow', params: { id: project.uuid } }"
						class="btn-gradient-primary btn-sm gap-2"
					>
						<PencilSquareIcon class="w-4 h-4" />
						{{ t("edit") }}
					</RouterLink>
				</template>
				<template v-else>
					<button
						@click="emit('open-folder', project)"
						class="btn-outline-primary btn-sm gap-2"
					>
						<FolderIcon class="w-4 h-4" />
						{{ t("open_folder") }}
					</button>
				</template>
			</div>
		</div>
	</div>
</template>
