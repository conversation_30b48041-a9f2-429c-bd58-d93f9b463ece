<template>
	<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
		<div
			v-for="i in 12"
			:key="i"
			class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-6 animate-pulse border border-slate-200"
		>
			<div class="h-5 bg-slate-200 rounded w-3/4 mb-4"></div>
			<div class="h-4 bg-slate-200 rounded w-1/2 mb-3"></div>
			<div class="h-4 bg-slate-200 rounded w-1/4 mb-4"></div>
			<div class="flex justify-between items-center">
				<div class="h-8 bg-slate-200 rounded w-8"></div>
				<div class="flex gap-2">
					<div class="h-8 bg-slate-200 rounded w-16"></div>
					<div class="h-8 bg-slate-200 rounded w-16"></div>
				</div>
			</div>
		</div>
	</div>
</template>
