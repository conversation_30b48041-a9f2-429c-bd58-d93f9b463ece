<script setup lang="ts">
	import { useI18n } from "vue-i18n";
	import { RouterLink } from "vue-router";

	defineProps<{
		breadcrumbs: { uuid: string; name: string }[];
	}>();

	const emit = defineEmits<{
		(e: "go-back-root"): void;
		(e: "navigate", index: number): void;
	}>();

	const { t } = useI18n();
</script>

<template>
	<div class="flex items-center text-sm text-slate-600 gap-2">
		<RouterLink
			:to="{ name: 'projects' }"
			class="cursor-pointer hover:text-blue-600"
			@click.native="emit('go-back-root')"
		>
			{{ t("root") }}
		</RouterLink>
		<span v-for="(b, idx) in breadcrumbs" :key="b.uuid" class="flex items-center gap-2">
			<span>/</span>
			<RouterLink
				:to="{ name: 'projects', params: { folderId: b.uuid } }"
				class="cursor-pointer hover:text-blue-600"
				@click.native="emit('navigate', idx)"
			>
				{{ b.name }}
			</RouterLink>
		</span>
	</div>
</template>
