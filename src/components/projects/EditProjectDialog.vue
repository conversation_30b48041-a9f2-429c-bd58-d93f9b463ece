<script setup lang="ts">
	import { ref, watch } from "vue";
	import { useI18n } from "vue-i18n";
	import { updateProject, type Project } from "@/api/projects";
	import { Error, Success } from "@/utils/notify";

	const { t } = useI18n();

	const props = defineProps<{
		show: boolean;
		project: Project | null;
	}>();
	const emit = defineEmits(["close", "updated"]);

	const showDialog = ref(false);
	const name = ref("");
	const description = ref("");
	const isLoading = ref(false);

	watch(
		() => props.show,
		(newVal) => {
			showDialog.value = newVal;
			if (newVal && props.project) {
				name.value = props.project.name;
				description.value = props.project.description || "";
			}
		}
	);

	const closeDialog = () => {
		emit("close");
	};

	const handleUpdateProject = async () => {
		if (!name.value.trim()) {
			Error(t("error"), t("project-name-cannot-be-empty"));
			return;
		}

		if (!props.project) {
			Error(t("error"), t("no-project-selected"));
			return;
		}

		isLoading.value = true;
		const updatedProject: Project = {
			...props.project,
			name: name.value.trim(),
			description: description.value.trim(),
		};

		try {
			await updateProject(props.project.uuid, updatedProject);
			Success(t("success"), t("update-success"));
			emit("updated");
			closeDialog();
		} catch (error) {
			Error(t("update-failed"), (error as string) || t("operation-failed"));
		} finally {
			isLoading.value = false;
		}
	};
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': showDialog }">
			<div class="modal-box relative">
				<h3 class="font-bold text-xl">{{ t("edit-project") }}</h3>
				<div class="py-4">
					<label class="label">
						<span class="label-text">{{ t("project-name") }}</span>
					</label>
					<input
						type="text"
						v-model="name"
						class="input input-bordered w-full"
						:placeholder="t('please-enter-project-name')"
					/>
					<label class="label mt-4">
						<span class="label-text">{{ t("project-description") }}</span>
					</label>
					<textarea
						v-model="description"
						class="textarea textarea-bordered w-full"
						:placeholder="t('please-enter-project-description')"
					></textarea>
				</div>
				<div class="modal-action">
					<button class="btn" @click="closeDialog">{{ t("cancel") }}</button>
					<button
						class="btn btn-primary"
						@click="handleUpdateProject"
						:disabled="isLoading"
					>
						<span
							v-if="isLoading"
							class="loading loading-spinner loading-sm"
						></span>
						{{ t("save") }}
					</button>
				</div>
			</div>
		</div>
	</Teleport>
</template>
