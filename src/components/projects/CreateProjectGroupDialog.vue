<script setup lang="ts">
	import { ref, watch } from "vue";
	import { useI18n } from "vue-i18n";
	import { createProject, type Project } from "@/api/projects";
	import { Error, Success } from "@/utils/notify";

	const { t } = useI18n();

	const props = defineProps<{
		show: boolean;
		currentFolderId: string;
	}>();
	const emit = defineEmits(["close", "created"]);

	const showDialog = ref(false);
	const name = ref("");
	const description = ref("");
	const isLoading = ref(false);

	watch(
		() => props.show,
		(newVal) => {
			showDialog.value = newVal;
			if (newVal) {
				name.value = "";
				description.value = "";
			}
		}
	);

	const closeDialog = () => {
		emit("close");
	};

	const handleCreateProjectGroup = async () => {
		if (!name.value.trim()) {
			Error(t("error"), t("project-name-cannot-be-empty"));
			return;
		}

		isLoading.value = true;
		const projectGroup: Partial<Project> = {
			name: name.value.trim(),
			description: description.value.trim(),
			type: "project",
			parent_workflow_id: props.currentFolderId || "0",
			data: "{}",
		};

		try {
			await createProject(projectGroup as Project);
			Success(t("success"), t("create-success"));
			emit("created");
			closeDialog();
		} catch (error) {
			Error(t("create-failed"), (error as string) || t("operation-failed"));
		} finally {
			isLoading.value = false;
		}
	};
</script>

<template>
	<Teleport to="body">
		<div class="modal" :class="{ 'modal-open': showDialog }">
			<div class="modal-box relative">
				<h3 class="font-bold text-xl">{{ t("create-new-project-group") }}</h3>
				<div class="py-4">
					<label class="label">
						<span class="label-text">{{ t("project-group-name") }}</span>
					</label>
					<input
						type="text"
						v-model="name"
						class="input input-bordered w-full"
						:placeholder="t('please-enter-project-group-name')"
					/>
					<label class="label mt-4">
						<span class="label-text">{{ t("project-group-description") }}</span>
					</label>
					<textarea
						v-model="description"
						class="textarea textarea-bordered w-full"
						:placeholder="t('please-enter-project-group-description')"
					></textarea>
				</div>
				<div class="modal-action">
					<button class="btn" @click="closeDialog">{{ t("cancel") }}</button>
					<button
						class="btn btn-primary"
						@click="handleCreateProjectGroup"
						:disabled="isLoading"
					>
						<span
							v-if="isLoading"
							class="loading loading-spinner loading-sm"
						></span>
						{{ t("create") }}
					</button>
				</div>
			</div>
		</div>
	</Teleport>
</template>
