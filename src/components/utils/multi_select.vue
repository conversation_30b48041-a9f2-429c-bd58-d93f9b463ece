<template>
	<div class="multi-select">
		<!-- 已选择选项 -->
		<div class="selected-options mb-3 flex flex-col gap-2" v-if="modelValue.length > 0">
			<div
				v-for="value in modelValue"
				:key="value"
				class="selected-option flex items-center bg-primary/10 px-3 py-2 rounded-md"
			>
				<div class="flex-grow">
					<div class="font-semibold text-sm">{{ getOptionName(value) }}</div>
					<div class="text-xs text-base-content/70">
						{{ getOptionDescription(value) }}
					</div>
				</div>
				<button
					@click="removeOption(value)"
					class="text-gray-500 hover:text-error ml-2"
				>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						class="h-4 w-4"
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M6 18L18 6M6 6l12 12"
						/>
					</svg>
				</button>
			</div>
		</div>

		<!-- 自定义下拉选择框 -->
		<div class="relative" ref="dropdownContainer">
			<div
				class="select select-bordered w-full flex items-center justify-between"
				:class="{
					'cursor-not-allowed bg-base-200/50': modelValue.length >= options.length,
					'cursor-pointer': modelValue.length < options.length,
				}"
				@click="toggleDropdown"
			>
				<span>{{ t("pls-select", "Please Select") }}</span>
			</div>

			<div
				v-if="isDropdownOpen"
				class="absolute z-10 w-full mt-1 bg-base-100 border rounded-md shadow-lg max-h-60 flex flex-col"
			>
				<div class="p-2 border-b border-base-200">
					<input
						ref="searchInput"
						type="text"
						v-model="searchTerm"
						:placeholder="t('search-options', 'Search options...')"
						class="input input-sm input-bordered w-full"
						@click.stop
					/>
				</div>
				<ul class="overflow-y-auto">
					<li
						v-if="filteredOptions.length === 0"
						class="px-4 py-2 text-base-content/50"
					>
						{{ t("no-options-found", "No options found") }}
					</li>
					<li
						v-for="option in filteredOptions"
						:key="option.value"
						@click="selectOption(option)"
						class="px-4 py-2 hover:bg-base-200 cursor-pointer"
					>
						<div class="font-semibold">{{ option.name }}</div>
						<div v-if="option.description" class="text-xs text-base-content/70">
							{{ option.description }}
						</div>
					</li>
				</ul>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
	import type { PropType } from "vue";
	import { useI18n } from "vue-i18n";

	const { t } = useI18n();

	type Option = { value: string; name: string; description?: string };

	const props = defineProps({
		modelValue: {
			type: Array as PropType<string[]>,
			default: () => [],
		},
		options: {
			type: Array as PropType<Option[]>,
			required: true,
		},
	});

	const emit = defineEmits(["update:modelValue"]);

	const isDropdownOpen = ref(false);
	const dropdownContainer = ref<HTMLDivElement | null>(null);
	const searchInput = ref<HTMLInputElement | null>(null);
	const searchTerm = ref("");

	// 计算可用选项（排除已选择的）
	const availableOptions = computed(() => {
		return props.options.filter((option) => !props.modelValue.includes(option.value));
	});

	// 根据搜索词过滤选项
	const filteredOptions = computed(() => {
		if (!searchTerm.value) {
			return availableOptions.value;
		}
		const lowerCaseSearch = searchTerm.value.toLowerCase();
		return availableOptions.value.filter((option) => {
			const nameMatch = option.name.toLowerCase().includes(lowerCaseSearch);
			const descriptionMatch =
				option.description?.toLowerCase().includes(lowerCaseSearch) ?? false;
			return nameMatch || descriptionMatch;
		});
	});

	const toggleDropdown = () => {
		if (props.modelValue.length < props.options.length) {
			isDropdownOpen.value = !isDropdownOpen.value;
		}
	};

	watch(isDropdownOpen, (isOpen) => {
		if (isOpen) {
			nextTick(() => {
				searchInput.value?.focus();
			});
		} else {
			searchTerm.value = ""; // 关闭时清空搜索词
		}
	});

	const selectOption = (option: Option) => {
		if (!props.modelValue.includes(option.value)) {
			const newValue = [...props.modelValue, option.value];
			emit("update:modelValue", newValue);
		}
		isDropdownOpen.value = false;
	};

	// 移除选项
	const removeOption = (value: string) => {
		const newValue = props.modelValue.filter((v) => v !== value);
		emit("update:modelValue", newValue);
	};

	// 获取选项名称
	const getOptionName = (value: string) => {
		const option = props.options.find((opt) => opt.value === value);
		return option ? option.name : value;
	};

	// 获取选项描述
	const getOptionDescription = (value: string) => {
		const option = props.options.find((opt) => opt.value === value);
		return option?.description ?? "";
	};

	const handleClickOutside = (event: MouseEvent) => {
		if (
			dropdownContainer.value &&
			!dropdownContainer.value.contains(event.target as Node)
		) {
			isDropdownOpen.value = false;
		}
	};

	onMounted(() => {
		document.addEventListener("click", handleClickOutside);
	});

	onUnmounted(() => {
		document.removeEventListener("click", handleClickOutside);
	});
</script>

<style scoped>
	.multi-select {
		width: 100%;
	}
</style>
